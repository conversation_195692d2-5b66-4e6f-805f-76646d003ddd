#配置数据源
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          type: com.alibaba.druid.pool.DruidDataSource
          driverClassName: net.sf.log4jdbc.sql.jdbcapi.DriverSpy
          url: jdbc:log4jdbc:mysql://${DB_HOST:127.0.0.1}:${DB_PORT:3306}/${DB_NAME:master_cs}?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false
          username: ${DB_USER:root}
          password: ${DB_PWD:123456}
          druid:
            # 初始连接数
            initial-size: 5
            # 最小连接数
            min-idle: 15
            # 最大连接数
            max-active: 30
            # 超时时间(以秒数为单位)
            remove-abandoned-timeout: 180
            # 获取连接超时时间
            max-wait: 3000
            # 连接有效性检测时间
            time-between-eviction-runs-millis: 60000
            # 连接在池中最小生存的时间
            min-evictable-idle-time-millis: 300000
            # 连接在池中最大生存的时间
            max-evictable-idle-time-millis: 900000
            # 指明连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除
            test-while-idle: false
            # 指明是否在从池中取出连接前进行检验,如果检验失败, 则从池中去除连接并尝试取出另一个
            test-on-borrow: true
            # 是否在归还到池中前进行检验
            test-on-return: false
            # 检测连接是否有效
            validation-query: select 1
            # 配置监控统计
#            webStatFilter:
#              enabled: true
#            stat-view-servlet:
#              enabled: true
#              url-pattern: /druid/*
#              reset-enable: false
#            filter:
#              stat:
#                enabled: true
#                # 记录慢SQL
#                log-slow-sql: true
#                slow-sql-millis: 1000
#                merge-sql: true
#              wall:
#                config:
#                  multi-statement-allow: true
        wangmeng:
          type: com.alibaba.druid.pool.DruidDataSource
          driverClassName: net.sf.log4jdbc.sql.jdbcapi.DriverSpy
          url: jdbc:log4jdbc:mysql://${DB_HOST:127.0.0.1}:${DB_PORT:3306}/${DB_NAME:master_cs}?serverTimezone=Asia/Shanghai&characterEncoding=utf8&useSSL=false
          username: ${DB_USER:root}
          password: ${DB_PWD:123456}
          druid:
            # 初始连接数
            initial-size: 5
            # 最小连接数
            min-idle: 15
            # 最大连接数
            max-active: 30
            # 超时时间(以秒数为单位)
            remove-abandoned-timeout: 180
            # 获取连接超时时间
            max-wait: 3000
            # 连接有效性检测时间
            time-between-eviction-runs-millis: 60000
            # 连接在池中最小生存的时间
            min-evictable-idle-time-millis: 300000
            # 连接在池中最大生存的时间
            max-evictable-idle-time-millis: 900000
            # 指明连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除
            test-while-idle: true
            # 指明是否在从池中取出连接前进行检验,如果检验失败, 则从池中去除连接并尝试取出另一个
            test-on-borrow: true
            # 是否在归还到池中前进行检验
            test-on-return: false
            # 检测连接是否有效
            validation-query: select 1
            # 配置监控统计
    #        webStatFilter:
    #          enabled: true
    #        stat-view-servlet:
    #          enabled: true
    #          url-pattern: /druid/*
    #          reset-enable: false
    #        filter:
    #          stat:
    #            enabled: true
    #            # 记录慢SQL
    #            log-slow-sql: true
    #            slow-sql-millis: 1000
    #            merge-sql: true
    #          wall:
    #            config:
    #              multi-statement-allow: true

#spring:
#  autoconfigure:
#mybatis:
#  mapper-locations: "classpath:mapper/*.xml"

jwt:
  secret: UAfauG9rrNdRyxhd8BZBPXfL8iJ413b1MagJZ0FxgU9FFaIGsLUAPMA8/2ynUkiBfVCfu0tRMiLZ/oU+A34ZaA==

client:
  id: amzn1.application-oa2-client.2797fe8ed5f1484d96e0a1f04faa2b1e
  secret: amzn1.oa2-cs.v1.8c152ec95cd8fe7f53b68bdd5a3256faef675d167d75f4e85a51d1318b4ec48b

url:
  token_url: https://api.amazon.com/auth/o2/token
  api_url: https://advertising-api.amazon.com
  redirect_uri: https://ads.wkefu.com/api/v1/account/auth/
  location: https://ads.wkefu.com
  reports_url: https://sellingpartnerapi-na.amazon.com

job:
  corn:
    profile: 0 30 18 * * ?
    campaign_group_keyword: 0 0 4 * * ?
    create_report: 0 10 5 * * ?
    get_report: 0 0/25 8 * * ?
    daily_advertiser: 0 30 7 * * ?
    auto_negative: 0 6 23 * * ?
    exchange_rate: 0 0 1 1 * ?

oss:
  endpoint: http://oss-cn-shanghai.aliyuncs.com
  access_key_id: LTAI5tR5cFuXmPhVbngNVmT6
  access_key_secret: ******************************
  bucket_name: yiyitech-ads
  folder: report/

amazon:
  spapi:
    clientId: amzn1.application-oa2-client.725b6fe6fdc344128acec37d94e546fb
    clientSecret: amzn1.oa2-cs.v1.7ef79152ef80334187d089016ab78f78c71546548948524e41cf5027835d4071
    redirectUri: https://ads.wkefu.com/api/v1/account/spauth/
    api_url: https://advertising-api.amazon.com
