server:
  port: ${SERVER_PORT:8090}

spring:
  freemarker:
    check-template-location: false
  profiles:
    active: local
  jackson:
    time-zone: GMT+8
  data:
    redis:
      repositories:
        enabled: false

redis:
  #数据库索引
  database: ${REDIS_DB:1}
  host: ${REDIS_HOST:************}
  port: ${REDIS_PORT:30000}
  #连接超时时间
  timeout: 5000
  password: ${REDIS_PWD:Ywc201405}

yiyitech:
  cache:
    lettuce:
      enabled: true
    host: ${REDIS_HOST:************}
    port: ${REDIS_PORT:30000}
    password: ${REDIS_PWD:Ywc201405}
    database: ${REDIS_DB:1}
    connectTimeout: 5000
