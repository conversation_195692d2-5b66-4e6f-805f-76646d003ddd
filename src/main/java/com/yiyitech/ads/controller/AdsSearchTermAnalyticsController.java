package com.yiyitech.ads.controller;

import cn.hutool.core.util.StrUtil;
import com.yiyitech.ads.job.AdsSearchTermAnalyticsTask;
import com.yiyitech.ads.model.AdsSearchTermAnalyticsModel;
import com.yiyitech.ads.model.AdsSearchTermCategoryRelationModel;
import com.yiyitech.ads.model.AdsSearchTermProductRankingModel;
import com.yiyitech.ads.service.AdsSearchTermAnalyticsService;
import com.yiyitech.ads.service.AmazoService;
import com.yiyitech.ads.service.impl.AdsSearchTermAnalyticsServiceImpl;
import com.yiyitech.ads.mapper.AdsSearchTermAnalyticsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 搜索词分析控制器
 * 提供搜索词分析相关的API接口
 * 包括数据查询、同步、分析等功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName AdsSearchTermAnalyticsController.java
 * @Description 搜索词分析控制器
 * @createTime 2025年01月31日
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/ads/search-term-analytics")
public class AdsSearchTermAnalyticsController {

    @Autowired
    private AdsSearchTermAnalyticsService searchTermAnalyticsService;

    @Autowired
    private AdsSearchTermAnalyticsMapper searchTermAnalyticsMapper;

    @Autowired
    private AdsSearchTermAnalyticsTask searchTermAnalyticsTask;

    @Autowired
    private AmazoService amazoService;

    /**
     * 获取搜索词分析数据列表
     * 简化版接口，只需要时间和类目两个可选条件
     *
     * @param reportDate 报告日期（可选，格式：YYYY-MM-DD）
     * @param category 类目名称（可选）
     * @param pageNum 页码（默认1）
     * @param pageSize 页大小（默认20）
     * @return 搜索词分析数据列表
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getSearchTermAnalyticsList(
            @RequestParam(required = false) String reportDate,
            @RequestParam(required = false) String category,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize) {

        log.info("查询搜索词分析数据列表 - 报告日期: {}, 类目: {}, 页码: {}, 页大小: {}",
                reportDate, category, pageNum, pageSize);

        try {
            Map<String, Object> result = searchTermAnalyticsService.getSearchTermAnalyticsListSimple(
                    reportDate, category, pageNum, pageSize);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("查询搜索词分析数据列表异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取搜索词详细信息
     * 包含搜索词基本信息、关联类目、关联商品等
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @return 搜索词详细信息
     */
    @GetMapping("/details")
    public ResponseEntity<Map<String, Object>> getSearchTermDetails(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String searchTerm) {
        
        log.info("查询搜索词详细信息 - 账户: {}, 配置: {}, 搜索词: {}", accountId, profileId, searchTerm);
        
        try {
            Map<String, Object> result = searchTermAnalyticsService.getSearchTermDetails(
                    accountId, profileId, searchTerm);
            
            return new ResponseEntity<>(result, HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("查询搜索词详细信息异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 搜索词与类目组合查询接口
     * 查询所有搜索词数据，每个搜索词包含其点击量前三的类目
     *
     * @param reportDate 报告日期（可选，格式：YYYY-MM-DD）
     * @param pageNum 页码（默认1）
     * @param pageSize 页大小（默认20）
     * @return 所有搜索词信息和对应的前三类目数据
     */
    @GetMapping("/search-with-categories")
    public ResponseEntity<Map<String, Object>> getAllSearchTermsWithTopCategories(
            @RequestParam(required = false) String reportDate,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize) {

        log.info("搜索词与类目组合查询（所有数据） - 报告日期: {}, 页码: {}, 页大小: {}", reportDate, pageNum, pageSize);

        try {
            Map<String, Object> result = searchTermAnalyticsService.getAllSearchTermsWithTopCategories(
                    reportDate, pageNum, pageSize);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("搜索词与类目组合查询异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取搜索词关联的类目数据
     * 返回点击量前三的类目
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @return 关联类目列表
     */
    @GetMapping("/categories")
    public ResponseEntity<Map<String, Object>> getSearchTermCategories(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String searchTerm) {
        
        log.info("查询搜索词关联类目 - 账户: {}, 配置: {}, 搜索词: {}", accountId, profileId, searchTerm);
        
        try {
            List<AdsSearchTermCategoryRelationModel> categories = 
                    searchTermAnalyticsService.getSearchTermCategories(accountId, profileId, searchTerm);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", categories);
            result.put("count", categories.size());
            
            return new ResponseEntity<>(result, HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("查询搜索词关联类目异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取搜索词关联的商品排名数据
     * 返回点击量前三的商品
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @return 商品排名列表
     */
    @GetMapping("/products")
    public ResponseEntity<Map<String, Object>> getSearchTermProductRankings(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String searchTerm) {
        
        log.info("查询搜索词关联商品排名 - 账户: {}, 配置: {}, 搜索词: {}", accountId, profileId, searchTerm);
        
        try {
            List<AdsSearchTermProductRankingModel> products = 
                    searchTermAnalyticsService.getSearchTermProductRankings(accountId, profileId, searchTerm);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", products);
            result.put("count", products.size());
            
            return new ResponseEntity<>(result, HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("查询搜索词关联商品排名异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取热门搜索词列表
     * 按搜索量排序
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param limit 返回数量限制（默认10）
     * @return 热门搜索词列表
     */
    @GetMapping("/top-search-terms")
    public ResponseEntity<Map<String, Object>> getTopSearchTerms(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        log.info("查询热门搜索词 - 账户: {}, 配置: {}, 限制: {}", accountId, profileId, limit);
        
        try {
            List<AdsSearchTermAnalyticsModel> topSearchTerms = 
                    searchTermAnalyticsService.getTopSearchTerms(accountId, profileId, limit);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", topSearchTerms);
            result.put("count", topSearchTerms.size());
            
            return new ResponseEntity<>(result, HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("查询热门搜索词异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取搜索词趋势数据
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 趋势数据
     */
    @GetMapping("/trends")
    public ResponseEntity<Map<String, Object>> getSearchTermTrends(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String searchTerm,
            @RequestParam String startDate,
            @RequestParam String endDate) {
        
        log.info("查询搜索词趋势数据 - 账户: {}, 配置: {}, 搜索词: {}", accountId, profileId, searchTerm);
        
        try {
            Map<String, Object> result = searchTermAnalyticsService.getSearchTermTrends(
                    accountId, profileId, searchTerm, startDate, endDate);
            
            return new ResponseEntity<>(result, HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("查询搜索词趋势数据异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 分析搜索词竞争情况
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @return 竞争分析结果
     */
    @GetMapping("/competition-analysis")
    public ResponseEntity<Map<String, Object>> analyzeSearchTermCompetition(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String searchTerm) {
        
        log.info("分析搜索词竞争情况 - 账户: {}, 配置: {}, 搜索词: {}", accountId, profileId, searchTerm);
        
        try {
            Map<String, Object> result = searchTermAnalyticsService.analyzeSearchTermCompetition(
                    accountId, profileId, searchTerm);
            
            return new ResponseEntity<>(result, HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("分析搜索词竞争情况异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "分析异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取搜索词建议
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param seedKeyword 种子关键词
     * @param limit 返回数量限制（默认10）
     * @return 搜索词建议列表
     */
    @GetMapping("/suggestions")
    public ResponseEntity<Map<String, Object>> getSearchTermSuggestions(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String seedKeyword,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        log.info("获取搜索词建议 - 账户: {}, 配置: {}, 种子词: {}", accountId, profileId, seedKeyword);
        
        try {
            List<String> suggestions = searchTermAnalyticsService.getSearchTermSuggestions(
                    accountId, profileId, seedKeyword, limit);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", suggestions);
            result.put("count", suggestions.size());
            
            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("获取搜索词建议异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 手动同步搜索词分析数据
     *
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param marketplaceId 市场ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 同步结果
     */
    @PostMapping("/sync/analytics")
    public ResponseEntity<Map<String, Object>> syncSearchTermAnalyticsData(
            @RequestParam(required = false) String accountId,
            @RequestParam Long profileId,
            @RequestParam String marketplaceId,
            @RequestParam String startDate,
            @RequestParam String endDate) {

        log.info("手动同步搜索词分析数据 - 账户: {}, 配置: {}, 市场: {}, 日期: {} 到 {}",
                accountId, profileId, marketplaceId, startDate, endDate);

        try {
            // 如果没有提供accountId，自动获取第一个可用账户
            if (StrUtil.isBlank(accountId)) {
                accountId = searchTermAnalyticsService.getFirstAvailableAccountId();
                if (StrUtil.isBlank(accountId)) {
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("success", false);
                    errorResult.put("message", "没有找到可用的Amazon账户");
                    return new ResponseEntity<>(errorResult, HttpStatus.BAD_REQUEST);
                }
                log.info("自动选择账户: {}", accountId);
            }

            // 参数验证
            if (profileId == null || StrUtil.isBlank(marketplaceId) ||
                StrUtil.isBlank(startDate) || StrUtil.isBlank(endDate)) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("success", false);
                errorResult.put("message", "参数不能为空");
                return new ResponseEntity<>(errorResult, HttpStatus.BAD_REQUEST);
            }

            Map<String, Object> result = searchTermAnalyticsService.syncSearchTermAnalyticsData(
                    accountId, profileId, marketplaceId, startDate, endDate);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("手动同步搜索词分析数据异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "同步异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 手动同步搜索词类目关联数据
     *
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param marketplaceId 市场ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 同步结果
     */
    @PostMapping("/sync/categories")
    public ResponseEntity<Map<String, Object>> syncSearchTermCategoryRelations(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String marketplaceId,
            @RequestParam String startDate,
            @RequestParam String endDate) {

        log.info("手动同步搜索词类目关联数据 - 账户: {}, 配置: {}, 市场: {}", accountId, profileId, marketplaceId);

        try {
            Map<String, Object> result = searchTermAnalyticsService.syncSearchTermCategoryRelations(
                    accountId, profileId, marketplaceId, startDate, endDate);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("手动同步搜索词类目关联数据异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "同步异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 手动同步搜索词商品排名数据
     *
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param marketplaceId 市场ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 同步结果
     */
    @PostMapping("/sync/products")
    public ResponseEntity<Map<String, Object>> syncSearchTermProductRankings(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String marketplaceId,
            @RequestParam String startDate,
            @RequestParam String endDate) {

        log.info("手动同步搜索词商品排名数据 - 账户: {}, 配置: {}, 市场: {}", accountId, profileId, marketplaceId);

        try {
            Map<String, Object> result = searchTermAnalyticsService.syncSearchTermProductRankings(
                    accountId, profileId, marketplaceId, startDate, endDate);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("手动同步搜索词商品排名数据异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "同步异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 手动触发完整数据同步
     * 包括搜索词分析、类目关联、商品排名数据
     *
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param marketplaceId 市场ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 同步结果
     */
    @PostMapping("/sync/all")
    public ResponseEntity<Map<String, Object>> syncAllSearchTermData(
            @RequestParam String accountId,
            @RequestParam Long profileId,
            @RequestParam String marketplaceId,
            @RequestParam String startDate,
            @RequestParam String endDate) {

        log.info("手动触发完整数据同步 - 账户: {}, 配置: {}, 市场: {}, 日期: {} 到 {}",
                accountId, profileId, marketplaceId, startDate, endDate);

        try {
            Map<String, Object> result = searchTermAnalyticsTask.manualSyncSearchTermData(
                    accountId, profileId, marketplaceId, startDate, endDate);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("手动触发完整数据同步异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "同步异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取数据同步状态
     *
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @return 同步状态信息
     */
    @GetMapping("/sync/status")
    public ResponseEntity<Map<String, Object>> getSyncStatus(
            @RequestParam String accountId,
            @RequestParam Long profileId) {

        log.info("查询数据同步状态 - 账户: {}, 配置: {}", accountId, profileId);

        try {
            // 这里可以实现同步状态查询逻辑
            // 例如查询最近的同步时间、同步数量等
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "同步状态查询功能待实现");

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("查询数据同步状态异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 测试搜索词类目关联数据同步
     * 使用默认参数快速测试类目关联数据同步功能
     *
     * @return 同步结果
     */
    @PostMapping("/sync/categories/test")
    public ResponseEntity<Map<String, Object>> testSyncCategoryRelations() {
        log.info("测试搜索词类目关联数据同步 - 使用默认参数");

        try {
            // 自动获取第一个可用账户
            String accountId = searchTermAnalyticsService.getFirstAvailableAccountId();
            if (StrUtil.isBlank(accountId)) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("success", false);
                errorResult.put("message", "没有找到可用的Amazon账户");
                return new ResponseEntity<>(errorResult, HttpStatus.BAD_REQUEST);
            }

            // 使用默认参数
            Long profileId = ((AdsSearchTermAnalyticsServiceImpl) searchTermAnalyticsService)
                    .getFirstAvailableProfileId(accountId);  // 自动获取真实的profileId
            String marketplaceId = "ATVPDKIKX0DER";  // 美国市场
            String startDate = "2025-07-01";
            String endDate = "2025-07-30";

            log.info("测试类目关联同步 - 账户: {}, 配置: {}, 市场: {}, 日期: {} 到 {}",
                    accountId, profileId, marketplaceId, startDate, endDate);

            // 调用搜索词类目关联数据同步
            Map<String, Object> result = searchTermAnalyticsService.syncSearchTermCategoryRelations(
                    accountId, profileId, marketplaceId, startDate, endDate);

            // 添加测试信息
            Map<String, Object> testInfo = new HashMap<>();
            testInfo.put("testType", "搜索词类目关联数据同步测试");
            testInfo.put("accountId", accountId);
            testInfo.put("profileId", profileId);
            testInfo.put("marketplaceId", marketplaceId);
            testInfo.put("dateRange", startDate + " 到 " + endDate);
            testInfo.put("testTime", new java.util.Date().toString());
            result.put("testInfo", testInfo);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("测试搜索词类目关联数据同步异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "测试类目关联同步异常: " + e.getMessage());
            errorResult.put("errorDetails", e.getClass().getSimpleName() + ": " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 测试简化的搜索词分析数据列表查询
     * 只需要时间和类目两个可选条件
     *
     * @param reportDate 报告日期（可选）
     * @param category 类目名称（可选）
     * @return 查询结果
     */
    @GetMapping("/list/test")
    public ResponseEntity<Map<String, Object>> testSearchTermAnalyticsListSimple(
            @RequestParam(required = false) String reportDate,
            @RequestParam(required = false) String category) {

        log.info("测试简化的搜索词分析数据列表查询 - 报告日期: {}, 类目: {}", reportDate, category);

        try {
            Map<String, Object> result = searchTermAnalyticsService.getSearchTermAnalyticsListSimple(
                    reportDate, category, 1, 10); // 测试用小分页

            // 添加测试信息
            Map<String, Object> testInfo = new HashMap<>();
            testInfo.put("testType", "简化搜索词分析数据列表查询测试");
            testInfo.put("inputReportDate", reportDate != null ? reportDate : "未指定");
            testInfo.put("inputCategory", category != null ? category : "未指定");
            testInfo.put("testTime", new java.util.Date().toString());
            testInfo.put("apiEndpoint", "/api/v1/ads/search-term-analytics/list/test");
            result.put("testInfo", testInfo);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("测试简化搜索词分析数据列表查询异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "测试查询异常: " + e.getMessage());
            errorResult.put("errorDetails", e.getClass().getSimpleName() + ": " + e.getMessage());

            Map<String, Object> errorTestInfo = new HashMap<>();
            errorTestInfo.put("testType", "简化搜索词分析数据列表查询测试");
            errorTestInfo.put("inputReportDate", reportDate);
            errorTestInfo.put("inputCategory", category);
            errorTestInfo.put("errorTime", new java.util.Date().toString());
            errorResult.put("testInfo", errorTestInfo);

            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 测试搜索词与类目组合查询（所有数据）
     *
     * @param reportDate 报告日期（可选）
     * @return 测试结果
     */
    @GetMapping("/search-with-categories/test")
    public ResponseEntity<Map<String, Object>> testAllSearchTermsWithCategories(
            @RequestParam(required = false) String reportDate) {

        log.info("测试所有搜索词与类目组合查询 - 报告日期: {}", reportDate);

        try {
            // 使用小分页进行测试
            Map<String, Object> result = searchTermAnalyticsService.getAllSearchTermsWithTopCategories(
                    reportDate, 1, 5);

            // 添加测试信息
            Map<String, Object> testInfo = new HashMap<>();
            testInfo.put("testType", "所有搜索词与类目组合查询测试");
            testInfo.put("inputReportDate", reportDate != null ? reportDate : "未指定");
            testInfo.put("testPageSize", 5);
            testInfo.put("testTime", new java.util.Date().toString());
            testInfo.put("apiEndpoint", "/api/v1/ads/search-term-analytics/search-with-categories/test");
            result.put("testInfo", testInfo);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("测试所有搜索词与类目组合查询异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "测试查询异常: " + e.getMessage());
            errorResult.put("errorDetails", e.getClass().getSimpleName() + ": " + e.getMessage());

            Map<String, Object> errorTestInfo = new HashMap<>();
            errorTestInfo.put("testType", "所有搜索词与类目组合查询测试");
            errorTestInfo.put("inputReportDate", reportDate);
            errorTestInfo.put("errorTime", new java.util.Date().toString());
            errorResult.put("testInfo", errorTestInfo);

            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取类目点击量最高的关键词及其匹配商品
     * 先筛选出类目点击量最高的关键词，然后获取每个关键词的前三商品，
     * 根据商品标题与关键词的匹配度进行排序
     *
     * @param reportDate 报告日期（可选，格式：YYYY-MM-DD）
     * @param topKeywordsLimit 获取前N个关键词（默认10，最大50）
     * @return 关键词及其匹配度排序后的前三商品
     */
    @GetMapping("/top-keywords-with-matched-products")
    public ResponseEntity<Map<String, Object>> getTopKeywordsWithMatchedProducts(
            @RequestParam(required = false) String reportDate,
            @RequestParam(defaultValue = "10") Integer topKeywordsLimit) {

        log.info("获取类目点击量最高的关键词及其匹配商品 - 报告日期: {}, 关键词数量: {}", reportDate, topKeywordsLimit);

        try {
            Map<String, Object> result = searchTermAnalyticsService.getTopKeywordsWithMatchedProducts(
                    reportDate, topKeywordsLimit);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("获取关键词匹配商品异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "查询异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 测试关键词匹配商品功能
     *
     * @param reportDate 报告日期（可选）
     * @param topKeywordsLimit 关键词数量（可选，默认5）
     * @return 测试结果
     */
    @GetMapping("/top-keywords-with-matched-products/test")
    public ResponseEntity<Map<String, Object>> testTopKeywordsWithMatchedProducts(
            @RequestParam(required = false) String reportDate,
            @RequestParam(defaultValue = "5") Integer topKeywordsLimit) {

        log.info("测试关键词匹配商品功能 - 报告日期: {}, 关键词数量: {}", reportDate, topKeywordsLimit);

        try {
            Map<String, Object> result = searchTermAnalyticsService.getTopKeywordsWithMatchedProducts(
                    reportDate, topKeywordsLimit);

            // 添加测试信息
            Map<String, Object> testInfo = new HashMap<>();
            testInfo.put("testType", "关键词匹配商品功能测试");
            testInfo.put("inputReportDate", reportDate != null ? reportDate : "未指定");
            testInfo.put("inputTopKeywordsLimit", topKeywordsLimit);
            testInfo.put("testTime", new java.util.Date().toString());
            testInfo.put("apiEndpoint", "/api/v1/ads/search-term-analytics/top-keywords-with-matched-products/test");

            // 添加匹配度统计信息
            if (result.get("data") != null) {
                List<Map<String, Object>> keywords = (List<Map<String, Object>>) result.get("data");
                int totalProducts = 0;
                int highMatchProducts = 0; // 匹配度>=60%的商品

                for (Map<String, Object> keyword : keywords) {
                    List<Map<String, Object>> products = (List<Map<String, Object>>) keyword.get("topProducts");
                    if (products != null) {
                        totalProducts += products.size();
                        for (Map<String, Object> product : products) {
                            Double matchScore = (Double) product.get("matchScore");
                            if (matchScore != null && matchScore >= 60.0) {
                                highMatchProducts++;
                            }
                        }
                    }
                }

                testInfo.put("totalProducts", totalProducts);
                testInfo.put("highMatchProducts", highMatchProducts);
                testInfo.put("highMatchRatio", totalProducts > 0 ? String.format("%.1f%%", (double) highMatchProducts / totalProducts * 100) : "0%");
            }

            result.put("testInfo", testInfo);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("测试关键词匹配商品功能异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "测试异常: " + e.getMessage());
            errorResult.put("errorDetails", e.getClass().getSimpleName() + ": " + e.getMessage());

            Map<String, Object> errorTestInfo = new HashMap<>();
            errorTestInfo.put("testType", "关键词匹配商品功能测试");
            errorTestInfo.put("inputReportDate", reportDate);
            errorTestInfo.put("inputTopKeywordsLimit", topKeywordsLimit);
            errorTestInfo.put("errorTime", new java.util.Date().toString());
            errorResult.put("testInfo", errorTestInfo);

            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 测试类目字段显示
     * 专门测试category_name字段是否正确显示
     *
     * @param searchTerm 搜索词（可选，默认使用测试搜索词）
     * @return 测试结果
     */
    @GetMapping("/test-category-fields")
    public ResponseEntity<Map<String, Object>> testCategoryFields(
            @RequestParam(required = false, defaultValue = "wireless headphones") String searchTerm) {

        log.info("测试类目字段显示 - 搜索词: {}", searchTerm);

        try {
            Map<String, Object> result = searchTermAnalyticsService.getSearchTermWithTopCategories(
                    searchTerm, null);

            // 添加字段检查信息
            Map<String, Object> fieldCheck = new HashMap<>();
            fieldCheck.put("testType", "类目字段显示测试");
            fieldCheck.put("inputSearchTerm", searchTerm);
            fieldCheck.put("testTime", new java.util.Date().toString());

            // 检查返回的类目数据中是否包含category_name字段
            if (result.get("topCategories") != null) {
                List<Map<String, Object>> categories = (List<Map<String, Object>>) result.get("topCategories");
                if (!categories.isEmpty()) {
                    Map<String, Object> firstCategory = categories.get(0);
                    fieldCheck.put("categoryFieldsFound", firstCategory.keySet());
                    fieldCheck.put("hasCategoryName", firstCategory.containsKey("category_name"));
                    fieldCheck.put("categoryNameValue", firstCategory.get("category_name"));
                } else {
                    fieldCheck.put("categoryFieldsFound", "无类目数据");
                }
            } else {
                fieldCheck.put("categoryFieldsFound", "topCategories为null");
            }

            result.put("fieldCheck", fieldCheck);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("测试类目字段显示异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "测试异常: " + e.getMessage());
            errorResult.put("errorDetails", e.getClass().getSimpleName() + ": " + e.getMessage());

            Map<String, Object> errorFieldCheck = new HashMap<>();
            errorFieldCheck.put("testType", "类目字段显示测试");
            errorFieldCheck.put("inputSearchTerm", searchTerm);
            errorFieldCheck.put("errorTime", new java.util.Date().toString());
            errorResult.put("fieldCheck", errorFieldCheck);

            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 直接测试类目查询SQL
     * 直接调用Mapper方法查看原始SQL结果
     *
     * @param searchTerm 搜索词（可选，默认使用测试搜索词）
     * @return SQL查询原始结果
     */
    @GetMapping("/test-category-sql")
    public ResponseEntity<Map<String, Object>> testCategorySql(
            @RequestParam(required = false, defaultValue = "wireless headphones") String searchTerm) {

        log.info("直接测试类目查询SQL - 搜索词: {}", searchTerm);

        Map<String, Object> result = new HashMap<>();

        try {
            // 构建查询参数
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("searchTerm", searchTerm);

            // 直接调用Mapper方法
            List<Map<String, Object>> topCategories = searchTermAnalyticsMapper.getTopCategoriesBySearchTerm(queryParams);

            result.put("success", true);
            result.put("message", "SQL查询成功");
            result.put("searchTerm", searchTerm);
            result.put("rawSqlResult", topCategories);
            result.put("resultCount", topCategories != null ? topCategories.size() : 0);

            // 详细分析第一条记录的字段
            if (topCategories != null && !topCategories.isEmpty()) {
                Map<String, Object> firstRecord = topCategories.get(0);
                result.put("firstRecordFields", firstRecord.keySet());
                result.put("firstRecordData", firstRecord);
            }

            result.put("testTime", new java.util.Date().toString());

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("直接测试类目查询SQL异常: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "SQL查询异常: " + e.getMessage());
            result.put("errorDetails", e.getClass().getSimpleName() + ": " + e.getMessage());
            result.put("searchTerm", searchTerm);
            result.put("testTime", new java.util.Date().toString());

            return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 验证数据库连接和SQL映射
     * 测试简化查询方法是否正常工作
     *
     * @return 验证结果
     */
    @GetMapping("/verify/database")
    public ResponseEntity<Map<String, Object>> verifyDatabaseConnection() {
        log.info("验证数据库连接和SQL映射");

        Map<String, Object> result = new HashMap<>();

        try {
            // 测试简单查询（无条件，小分页）
            Map<String, Object> testResult = searchTermAnalyticsService.getSearchTermAnalyticsListSimple(
                    null, null, 1, 5);

            result.put("success", true);
            result.put("message", "数据库连接和SQL映射验证成功");
            result.put("testResult", testResult);
            result.put("verificationTime", new java.util.Date().toString());

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("数据库连接和SQL映射验证失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "验证失败: " + e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
            result.put("errorDetails", e.getMessage());
            result.put("verificationTime", new java.util.Date().toString());

            return new ResponseEntity<>(result, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 测试搜索词类目关联数据同步 - 带参数版本
     * 允许指定具体的测试参数
     *
     * @param accountId 账户ID（可选，默认自动获取）
     * @param profileId 配置文件ID（可选，默认自动获取）
     * @param marketplaceId 市场ID（可选，默认美国市场）
     * @param startDate 开始日期（可选，默认2025-07-01）
     * @param endDate 结束日期（可选，默认2025-07-30）
     * @return 同步结果
     */
    @PostMapping("/sync/categories/test-with-params")
    public ResponseEntity<Map<String, Object>> testSyncCategoryRelationsWithParams(
            @RequestParam(required = false) String accountId,
            @RequestParam(required = false) Long profileId,
            @RequestParam(required = false, defaultValue = "ATVPDKIKX0DER") String marketplaceId,
            @RequestParam(required = false, defaultValue = "2025-07-01") String startDate,
            @RequestParam(required = false, defaultValue = "2025-07-30") String endDate) {

        log.info("测试搜索词类目关联数据同步（带参数） - 账户: {}, 配置: {}, 市场: {}, 日期: {} 到 {}",
                accountId, profileId, marketplaceId, startDate, endDate);

        try {
            // 如果没有提供accountId，自动获取
            if (StrUtil.isBlank(accountId)) {
                accountId = searchTermAnalyticsService.getFirstAvailableAccountId();
                if (StrUtil.isBlank(accountId)) {
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("success", false);
                    errorResult.put("message", "没有找到可用的Amazon账户");
                    return new ResponseEntity<>(errorResult, HttpStatus.BAD_REQUEST);
                }
                log.info("自动选择账户: {}", accountId);
            }

            // 如果没有提供profileId，自动获取
            if (profileId == null) {
                profileId = ((AdsSearchTermAnalyticsServiceImpl) searchTermAnalyticsService)
                        .getFirstAvailableProfileId(accountId);
                if (profileId == null) {
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("success", false);
                    errorResult.put("message", "没有找到可用的Profile配置");
                    return new ResponseEntity<>(errorResult, HttpStatus.BAD_REQUEST);
                }
                log.info("自动选择Profile: {}", profileId);
            }

            // 参数验证
            if (StrUtil.isBlank(marketplaceId) || StrUtil.isBlank(startDate) || StrUtil.isBlank(endDate)) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("success", false);
                errorResult.put("message", "市场ID、开始日期、结束日期不能为空");
                return new ResponseEntity<>(errorResult, HttpStatus.BAD_REQUEST);
            }

            log.info("开始执行类目关联同步测试 - 最终参数: 账户={}, 配置={}, 市场={}, 日期={} 到 {}",
                    accountId, profileId, marketplaceId, startDate, endDate);

            // 调用搜索词类目关联数据同步
            Map<String, Object> result = searchTermAnalyticsService.syncSearchTermCategoryRelations(
                    accountId, profileId, marketplaceId, startDate, endDate);

            // 添加详细的测试信息
            Map<String, Object> testInfo = new HashMap<>();
            testInfo.put("testType", "搜索词类目关联数据同步测试（带参数）");
            testInfo.put("finalAccountId", accountId);
            testInfo.put("finalProfileId", profileId);
            testInfo.put("finalMarketplaceId", marketplaceId);
            testInfo.put("finalDateRange", startDate + " 到 " + endDate);
            testInfo.put("testTime", new java.util.Date().toString());
            testInfo.put("apiEndpoint", "/api/v1/ads/search-term-analytics/sync/categories/test-with-params");
            result.put("testInfo", testInfo);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("测试搜索词类目关联数据同步异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "测试类目关联同步异常: " + e.getMessage());
            errorResult.put("errorDetails", e.getClass().getSimpleName() + ": " + e.getMessage());
            Map<String, Object> errorTestInfo = new HashMap<>();
            errorTestInfo.put("testType", "搜索词类目关联数据同步测试（带参数）");
            errorTestInfo.put("inputAccountId", accountId);
            errorTestInfo.put("inputProfileId", profileId);
            errorTestInfo.put("inputMarketplaceId", marketplaceId);
            errorTestInfo.put("inputDateRange", startDate + " 到 " + endDate);
            errorTestInfo.put("errorTime", new java.util.Date().toString());
            errorResult.put("testInfo", errorTestInfo);
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 快速测试同步 - 使用默认参数和自动选择账户
     *
     * @return 同步结果
     */
    @PostMapping("/sync/test")
    public ResponseEntity<Map<String, Object>> testSyncWithDefaults() {
        log.info("快速测试同步 - 使用默认参数");

        try {
            // 自动获取第一个可用账户
            String accountId = searchTermAnalyticsService.getFirstAvailableAccountId();
            if (StrUtil.isBlank(accountId)) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("success", false);
                errorResult.put("message", "没有找到可用的Amazon账户");
                return new ResponseEntity<>(errorResult, HttpStatus.BAD_REQUEST);
            }

            // 使用默认参数
            Long profileId = ((AdsSearchTermAnalyticsServiceImpl) searchTermAnalyticsService)
                    .getFirstAvailableProfileId(accountId);  // 自动获取真实的profileId
            String marketplaceId = "ATVPDKIKX0DER";
            String startDate = "2025-07-21";
            String endDate = "2025-07-27";

            log.info("使用参数 - 账户: {}, 配置: {}, 市场: {}, 日期: {} 到 {}",
                    accountId, profileId, marketplaceId, startDate, endDate);

            Map<String, Object> result = searchTermAnalyticsService.syncSearchTermAnalyticsData(
                    accountId, profileId, marketplaceId, startDate, endDate);

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("快速测试同步异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "测试同步异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 初始化Redis中的Amazon账户token
     * 用于解决token过期问题
     *
     * @return 初始化结果
     */
    @PostMapping("/init/tokens")
    public ResponseEntity<Map<String, Object>> initializeTokens() {
        log.info("初始化Redis中的Amazon账户token");

        try {
            // 调用AmazoService的setRedis方法来初始化token
            amazoService.setRedis();

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "Amazon账户token初始化成功");

            return new ResponseEntity<>(result, HttpStatus.OK);

        } catch (Exception e) {
            log.error("初始化token异常: {}", e.getMessage(), e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "初始化token异常: " + e.getMessage());
            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 查询指定账户的refresh token信息
     *
     * @param accountId 账户ID（可选，默认使用A20YZH1DKSWMJL）
     * @return token信息
     */
//    @GetMapping("/account/token")
//    public ResponseEntity<Map<String, Object>> getAccountToken(
//            @RequestParam(required = false, defaultValue = "A20YZH1DKSWMJL") String accountId) {
//
//        log.info("查询账户 {} 的token信息", accountId);
//
//        try {
//            Map<String, Object> result = new HashMap<>();
//
//            // 获取账户信息
//            String refreshToken = ((AdsSearchTermAnalyticsServiceImpl) searchTermAnalyticsService)
//                    .getAccessTokenFromDatabase(accountId);
//
//            if (StrUtil.isNotBlank(refreshToken)) {
//                result.put("success", true);
//                result.put("accountId", accountId);
//                result.put("refreshTokenSp", refreshToken);
//                result.put("tokenLength", refreshToken.length());
//                result.put("tokenPreview", refreshToken.length() > 20 ?
//                        refreshToken.substring(0, 20) + "..." : refreshToken);
//                result.put("message", "成功获取refresh token");
//            } else {
//                result.put("success", false);
//                result.put("accountId", accountId);
//                result.put("message", "未找到有效的refresh token");
//            }
//
//            return new ResponseEntity<>(result, HttpStatus.OK);
//
//        } catch (Exception e) {
//            log.error("查询账户token异常: {}", e.getMessage(), e);
//            Map<String, Object> errorResult = new HashMap<>();
//            errorResult.put("success", false);
//            errorResult.put("message", "查询token异常: " + e.getMessage());
//            return new ResponseEntity<>(errorResult, HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }
}
