package com.yiyitech.ads.model.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * Amazon SP-API 创建报告响应模型
 * 专门用于处理createReport操作的响应
 * 根据Amazon SP API文档，创建报告时主要返回reportId和reportType
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName SpReportBaseResponse.java
 * @Description Amazon SP-API创建报告响应模型 - 只包含创建时的基本字段
 * @createTime 2025年01月31日
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class SpReportBaseResponse {

    // 报告ID - 创建报告时返回的唯一标识符
    private String reportId;

    // 报告类型 - 请求的报告类型
    private String reportType;


    /**
     * 检查是否有报告ID
     * @return true-有报告ID，false-无报告ID
     */
    public boolean hasReportId() {
        return reportId != null && !reportId.trim().isEmpty();
    }

    /**
     * 检查是否有报告类型
     * @return true-有报告类型，false-无报告类型
     */
    public boolean hasReportType() {
        return reportType != null && !reportType.trim().isEmpty();
    }
}
