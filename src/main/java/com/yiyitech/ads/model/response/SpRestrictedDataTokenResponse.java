package com.yiyitech.ads.model.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Amazon SP-API 受限数据令牌响应
 * 
 * 根据Amazon SP-API文档：
 * https://developer-docs.amazon.com/sp-api/lang-zh_CN/reference/createrestricteddatatoken
 * 
 * <AUTHOR> Agent
 * @date 2025-08-05
 */
@Data
public class SpRestrictedDataTokenResponse {

    /**
     * 受限数据令牌
     * 用于访问受限资源的令牌
     */
    @JsonProperty("restrictedDataToken")
    private String restrictedDataToken;

    /**
     * 令牌过期时间（秒）
     * 从创建时间开始计算的有效期
     */
    @JsonProperty("expiresIn")
    private Integer expiresIn;

    /**
     * 错误信息（如果请求失败）
     */
    @JsonProperty("error")
    private String error;

    /**
     * 错误描述（如果请求失败）
     */
    @JsonProperty("error_description")
    private String errorDescription;

    /**
     * 检查响应是否成功
     * 
     * @return 如果有受限数据令牌且无错误则返回true
     */
    public boolean isSuccess() {
        return restrictedDataToken != null && error == null;
    }

    /**
     * 获取错误信息
     * 
     * @return 完整的错误信息
     */
    public String getFullErrorMessage() {
        if (error == null) {
            return null;
        }
        
        StringBuilder sb = new StringBuilder(error);
        if (errorDescription != null) {
            sb.append(": ").append(errorDescription);
        }
        return sb.toString();
    }

    /**
     * 检查令牌是否即将过期
     * 
     * @param thresholdSeconds 阈值秒数
     * @return 如果令牌在阈值时间内过期则返回true
     */
    public boolean isExpiringSoon(int thresholdSeconds) {
        return expiresIn != null && expiresIn <= thresholdSeconds;
    }

    @Override
    public String toString() {
        return "SpRestrictedDataTokenResponse{" +
                "restrictedDataToken='" + (restrictedDataToken != null ? "***" : null) + '\'' +
                ", expiresIn=" + expiresIn +
                ", error='" + error + '\'' +
                ", errorDescription='" + errorDescription + '\'' +
                '}';
    }
}
