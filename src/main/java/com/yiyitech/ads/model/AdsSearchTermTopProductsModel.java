package com.yiyitech.ads.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 搜索词前三商品数据模型
 * 用于存储每个搜索词对应的前三名商品信息
 * 对应Amazon SP-API的Brand Analytics Search Terms Report中的前三商品数据
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName AdsSearchTermTopProductsModel.java
 * @Description 搜索词前三商品数据模型
 * @createTime 2025年01月31日
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ads_search_term_top_products")
public class AdsSearchTermTopProductsModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 账户ID
     */
    private String accountId;

    /**
     * 配置文件ID
     */
    private Long profileId;

    /**
     * 市场ID
     */
    private String marketplaceId;

    /**
     * 搜索词
     */
    private String searchTerm;

    /**
     * 商品排名（1-3）
     */
    private Integer productRank;

    /**
     * 商品ASIN
     */
    private String asin;

    /**
     * 商品标题
     */
    private String productTitle;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 点击次数
     */
    private Integer clicks;

    /**
     * 点击率
     */
    private Double clickThroughRate;

    /**
     * 转化次数
     */
    private Integer conversions;

    /**
     * 转化率
     */
    private Double conversionRate;

    /**
     * 销售额
     */
    private Double sales;

    /**
     * 订单数量
     */
    private Integer orders;

    /**
     * 商品图片URL
     */
    private String imageUrl;

    /**
     * 商品价格
     */
    private Double price;

    /**
     * 商品评分
     */
    private Double rating;

    /**
     * 评论数量
     */
    private Integer reviewCount;

    /**
     * 商品类目
     */
    private String category;

    /**
     * 商品子类目
     */
    private String subCategory;

    /**
     * 是否为自有品牌
     */
    private Boolean isOwnBrand;

    /**
     * 竞争强度
     */
    private Integer competitionIntensity;

    /**
     * 市场份额
     */
    private Double marketShare;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 报告日期
     */
    private Date reportDate;

    /**
     * 数据状态
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 计算点击率
     * @param impressions 展示次数
     * @return 点击率
     */
    public Double calculateClickThroughRate(Integer impressions) {
        if (impressions == null || impressions == 0 || clicks == null) {
            return 0.0;
        }
        return (double) clicks / impressions * 100;
    }

    /**
     * 计算转化率
     * @return 转化率
     */
    public Double calculateConversionRate() {
        if (clicks == null || clicks == 0 || conversions == null) {
            return 0.0;
        }
        return (double) conversions / clicks * 100;
    }

    /**
     * 检查是否为热门商品
     * @return true-热门商品，false-非热门商品
     */
    public boolean isPopularProduct() {
        return productRank != null && productRank <= 3 && 
               clicks != null && clicks > 100 &&
               conversionRate != null && conversionRate > 5.0;
    }

    /**
     * 获取商品排名描述
     * @return 排名描述
     */
    public String getRankDescription() {
        if (productRank == null) {
            return "未知排名";
        }
        
        switch (productRank) {
            case 1:
                return "第一名";
            case 2:
                return "第二名";
            case 3:
                return "第三名";
            default:
                return "第" + productRank + "名";
        }
    }
}
