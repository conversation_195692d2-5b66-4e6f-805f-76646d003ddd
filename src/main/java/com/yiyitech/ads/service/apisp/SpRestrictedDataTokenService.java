package com.yiyitech.ads.service.apisp;

import com.yiyitech.ads.model.response.SpRestrictedDataTokenResponse;
import com.yiyitech.ads.exception.BasicExceptionCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import com.yiyitech.support.exception.BusinessException;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Amazon SP-API 受限数据令牌(RDT)服务
 * 专门处理受限数据令牌的创建、管理和使用
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName SpRestrictedDataTokenService.java
 * @Description 独立的RDT服务，按需调用
 * @createTime 2025年08月06日
 */
@Slf4j
@Service
public class SpRestrictedDataTokenService {

    private final RestTemplate awsSignedRestTemplate;
    
    // RDT令牌缓存，避免频繁创建
    private final Map<String, CachedToken> tokenCache = new ConcurrentHashMap<>();

    @Value("${url.reports_url}")
    private String reports_url;

    public SpRestrictedDataTokenService(
            @Qualifier("awsSignedRestTemplate") RestTemplate awsSignedRestTemplate) {
        this.awsSignedRestTemplate = awsSignedRestTemplate;
    }

    /**
     * 缓存的令牌信息
     */
    private static class CachedToken {
        private final SpRestrictedDataTokenResponse tokenResponse;
        private final LocalDateTime createTime;

        public CachedToken(SpRestrictedDataTokenResponse tokenResponse) {
            this.tokenResponse = tokenResponse;
            this.createTime = LocalDateTime.now();
        }

        public boolean isExpired() {
            // 提前5分钟过期，确保安全
            long expiresInSeconds = tokenResponse.getExpiresIn() - 300;
            return LocalDateTime.now().isAfter(createTime.plusSeconds(expiresInSeconds));
        }

        public String getToken() {
            return tokenResponse.getRestrictedDataToken();
        }

        public SpRestrictedDataTokenResponse getResponse() {
            return tokenResponse;
        }
    }

    /**
     * 获取或创建RDT令牌（带缓存）
     * 
     * @param accessToken SP-API访问令牌
     * @param reportType 报告类型
     * @param marketplaceIds 市场ID列表
     * @return RDT令牌字符串
     */
    public String getOrCreateToken(String accessToken, String reportType, List<String> marketplaceIds) {
        String cacheKey = generateCacheKey(accessToken, reportType, marketplaceIds);
        
        CachedToken cachedToken = tokenCache.get(cacheKey);
        if (cachedToken != null && !cachedToken.isExpired()) {
            log.debug("使用缓存的RDT令牌 - 报告类型: {}", reportType);
            return cachedToken.getToken();
        }

        // 创建新的RDT令牌
        log.info("创建新的RDT令牌 - 报告类型: {}", reportType);
        List<Map<String, Object>> restrictedResources = createRestrictedResources(reportType, marketplaceIds);
        SpRestrictedDataTokenResponse newTokenResponse = createRestrictedDataToken(accessToken, restrictedResources);
        
        // 缓存新令牌
        tokenCache.put(cacheKey, new CachedToken(newTokenResponse));
        
        return newTokenResponse.getRestrictedDataToken();
    }

    /**
     * 直接创建RDT令牌（不使用缓存）
     * 
     * @param accessToken SP-API访问令牌
     * @param reportType 报告类型
     * @param marketplaceIds 市场ID列表
     * @return 完整的RDT响应
     */
    public SpRestrictedDataTokenResponse createTokenForReport(String accessToken, String reportType, List<String> marketplaceIds) {
        log.info("创建RDT令牌 - 报告类型: {}", reportType);
        List<Map<String, Object>> restrictedResources = createRestrictedResources(reportType, marketplaceIds);
        return createRestrictedDataToken(accessToken, restrictedResources);
    }

    /**
     * 为自定义资源创建RDT令牌
     * 
     * @param accessToken SP-API访问令牌
     * @param restrictedResources 自定义的受限资源列表
     * @return 完整的RDT响应
     */
    public SpRestrictedDataTokenResponse createTokenForCustomResources(String accessToken, List<Map<String, Object>> restrictedResources) {
        log.info("创建自定义RDT令牌 - 受限资源数量: {}", restrictedResources.size());
        return createRestrictedDataToken(accessToken, restrictedResources);
    }

    /**
     * 创建带RDT的HTTP请求头
     * 
     * @param rdtToken RDT令牌
     * @return HTTP请求头
     */
    public HttpHeaders createHeadersWithRDT(String rdtToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("x-amz-access-token", rdtToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept", "application/json");
        return headers;
    }

    /**
     * 清除指定的令牌缓存
     * 
     * @param accessToken SP-API访问令牌
     * @param reportType 报告类型
     * @param marketplaceIds 市场ID列表
     */
    public void clearTokenCache(String accessToken, String reportType, List<String> marketplaceIds) {
        String cacheKey = generateCacheKey(accessToken, reportType, marketplaceIds);
        tokenCache.remove(cacheKey);
        log.info("已清除RDT令牌缓存 - 报告类型: {}", reportType);
    }

    /**
     * 清除所有过期的令牌缓存
     */
    public void clearExpiredTokens() {
        int removedCount = 0;
        for (Map.Entry<String, CachedToken> entry : tokenCache.entrySet()) {
            if (entry.getValue().isExpired()) {
                tokenCache.remove(entry.getKey());
                removedCount++;
            }
        }
        if (removedCount > 0) {
            log.info("已清除 {} 个过期的RDT令牌", removedCount);
        }
    }

    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalCached", tokenCache.size());
        
        long expiredCount = tokenCache.values().stream()
                .mapToLong(token -> token.isExpired() ? 1 : 0)
                .sum();
        stats.put("expiredCount", expiredCount);
        stats.put("validCount", tokenCache.size() - expiredCount);
        
        return stats;
    }

    // ==================== 私有方法 ====================

    /**
     * 创建受限数据令牌
     */
    private SpRestrictedDataTokenResponse createRestrictedDataToken(String accessToken, List<Map<String, Object>> restrictedResources) {
        try {
            String url = reports_url + "/tokens/2021-03-01/restrictedDataToken";

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("restrictedResources", restrictedResources);

            log.debug("RDT请求URL: {}", url);
            log.debug("RDT请求体: {}", requestBody);

            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<SpRestrictedDataTokenResponse> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.POST, entity, SpRestrictedDataTokenResponse.class
            );

            if (response.getStatusCode() != HttpStatus.OK || response.getBody() == null) {
                log.error("创建受限数据令牌失败 - 状态码: {}, 响应体: {}", response.getStatusCode(), response.getBody());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建受限数据令牌失败");
            }

            SpRestrictedDataTokenResponse rdtResponse = response.getBody();
            if (!rdtResponse.isSuccess()) {
                log.error("受限数据令牌响应包含错误: {}", rdtResponse.getFullErrorMessage());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "RDT创建失败: " + rdtResponse.getFullErrorMessage());
            }

            log.info("受限数据令牌创建成功 - 令牌过期时间: {} 秒", rdtResponse.getExpiresIn());
            return rdtResponse;

        } catch (Exception e) {
            log.error("创建受限数据令牌异常: {}", e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建受限数据令牌异常: " + e.getMessage());
        }
    }

    /**
     * 为报告类型创建受限资源配置
     */
    private List<Map<String, Object>> createRestrictedResources(String reportType, List<String> marketplaceIds) {
        List<Map<String, Object>> restrictedResources = new ArrayList<>();

        Map<String, Object> resource = new HashMap<>();
        resource.put("method", "POST");
        resource.put("path", "/reports/2021-06-30/reports");

        // 根据报告类型可以添加更具体的配置
        // 目前使用通用配置，让Amazon API自动确定所需权限
        
        restrictedResources.add(resource);
        return restrictedResources;
    }

    /**
     * 创建普通HTTP请求头
     */
    private HttpHeaders createHeaders(String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("x-amz-access-token", accessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept", "application/json");
        return headers;
    }

    /**
     * 生成缓存键
     */
    private String generateCacheKey(String accessToken, String reportType, List<String> marketplaceIds) {
        return String.format("%s_%s_%s", 
            accessToken.substring(0, Math.min(10, accessToken.length())), 
            reportType, 
            String.join(",", marketplaceIds));
    }
}
