package com.yiyitech.ads.service.apisp;

import com.yiyitech.ads.model.response.SpReportBaseResponse;
import com.yiyitech.ads.model.response.SpReportStatusResponse;

import com.yiyitech.ads.exception.BasicExceptionCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import com.yiyitech.support.exception.BusinessException;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Amazon SP-API 分析报告服务
 * 用于获取Amazon SP-API的各种分析报告数据
 * 包括搜索词报告、搜索目录性能报告、市场篮分析等
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * &#064;ClassName  SpAnalyticsReportsApi.java
 * &#064;Description  Amazon SP-API分析报告服务，获取搜索词和类目相关数据
 * &#064;createTime  2025年01月31日
 */
@Slf4j
@Component
public class SpAnalyticsReportsApi {

    private final RestTemplate awsSignedRestTemplate;

    public SpAnalyticsReportsApi(
            @Qualifier("awsSignedRestTemplate") RestTemplate awsSignedRestTemplate) {
        this.awsSignedRestTemplate = awsSignedRestTemplate;
    }

    @Value("${url.reports_url}")
    private String reports_url;







    /**
     * 创建Amazon搜索词报告
     * 对应Amazon SP-API的Amazon Search Terms Report
     * 用于获取客户搜索词数据，显示客户在Amazon上搜索的词汇以及这些搜索如何转化为产品销售
     *
     * @param accessToken SP-API访问令牌
     * @param marketplaceIds 市场ID列表
     * @param startDate 开始日期 (YYYY-MM-DD)
     * @param endDate 结束日期 (YYYY-MM-DD)
     * @return 报告创建响应
     */
    public SpReportBaseResponse createSearchQueryPerformanceReport(String accessToken,
                                                                   List<String> marketplaceIds,
                                                                   String startDate,
                                                                   String endDate) {
        try {
            log.info("创建Amazon搜索词报告 - 开始日期: {}, 结束日期: {}, 市场: {}", startDate, endDate, marketplaceIds);

            String url = reports_url + "/reports/2021-06-30/reports";

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("reportType", "GET_BRAND_ANALYTICS_SEARCH_TERMS_REPORT");
            requestBody.put("marketplaceIds", marketplaceIds);
            requestBody.put("dataStartTime", startDate + "T00:00:00Z");
            requestBody.put("dataEndTime", endDate + "T23:59:59Z");

            // 报告选项
            Map<String, String> reportOptions = new HashMap<>();
            reportOptions.put("reportPeriod", "WEEK");
            reportOptions.put("brandId", "2970732");
            requestBody.put("reportOptions", reportOptions);

            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            log.debug("创建报告请求 - URL: {}, 请求体: {}", url, requestBody);

            ResponseEntity<SpReportBaseResponse> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.POST, entity, SpReportBaseResponse.class
            );

            if ((response.getStatusCode() != HttpStatus.ACCEPTED &&
                    response.getStatusCode() != HttpStatus.OK) ||
                    response.getBody() == null) {
                log.error("创建报告失败 - 状态码: {} 响应体: {}", response.getStatusCode(), response.getBody());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建报告失败");
            }

            log.info("报告创建成功 - 报告类型: {}, 报告ID: {}", "GET_BRAND_ANALYTICS_SEARCH_TERMS_REPORT", response.getBody().getReportId());
            return response.getBody();

        } catch (Exception e) {
            log.error("创建搜索词性能报告异常: {}", e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建搜索词性能报告异常: " + e.getMessage());
        }
    }






    /**
     * 创建搜索目录性能报告
     * 对应Amazon SP-API的Search Catalog Performance Report
     * 用于获取搜索词与商品类目的关联性能数据
     *
     * @param accessToken SP-API访问令牌
     * @param marketplaceIds 市场ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 报告创建响应
     */
    public SpReportBaseResponse createSearchCatalogPerformanceReport(String accessToken,
                                                                     List<String> marketplaceIds,
                                                                     String startDate,
                                                                     String endDate) {
        try {
            log.info("创建搜索目录性能报告 - 开始日期: {}, 结束日期: {}, 市场: {}", startDate, endDate, marketplaceIds);

            String url = reports_url + "/reports/2021-06-30/reports";

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("reportType", "GET_BRAND_ANALYTICS_SEARCH_CATALOG_PERFORMANCE_REPORT");
            requestBody.put("marketplaceIds", marketplaceIds);
            requestBody.put("dataStartTime", startDate + "T00:00:00Z");
            requestBody.put("dataEndTime", endDate + "T23:59:59Z");

            // 报告选项
            Map<String, String> reportOptions = new HashMap<>();
            reportOptions.put("reportPeriod", "WEEK");
            reportOptions.put("brandId", "2970732");
            requestBody.put("reportOptions", reportOptions);

            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<SpReportBaseResponse> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.POST, entity, SpReportBaseResponse.class
            );

            if ((response.getStatusCode() != HttpStatus.ACCEPTED &&
                    response.getStatusCode() != HttpStatus.OK) ||
                    response.getBody() == null) {
                log.error("创建搜索目录性能报告失败 - 状态码: {}", response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建搜索目录性能报告失败");
            }

            log.info("搜索目录性能报告创建成功 - 报告ID: {}", response.getBody().getReportId());
            return response.getBody();

        } catch (Exception e) {
            log.error("创建搜索目录性能报告异常: {}", e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建搜索目录性能报告异常: " + e.getMessage());
        }
    }

    /**
     * 创建Amazon搜索词报告
     * 对应Amazon SP-API的Amazon Search Terms Report
     * 用于获取Amazon平台的搜索词数据和趋势
     *
     * @param accessToken SP-API访问令牌
     * @param marketplaceIds 市场ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 报告创建响应
     */
    public SpReportBaseResponse createAmazonSearchTermsReport(String accessToken,
                                                              List<String> marketplaceIds,
                                                              String startDate,
                                                              String endDate) {
        try {
            log.info("创建Amazon搜索词报告 - 开始日期: {}, 结束日期: {}, 市场: {}", startDate, endDate, marketplaceIds);

            String url = reports_url + "/reports/2021-06-30/reports";

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("reportType", "GET_BRAND_ANALYTICS_AMAZON_SEARCH_TERMS_REPORT");
            requestBody.put("marketplaceIds", marketplaceIds);
            requestBody.put("dataStartTime", startDate + "T00:00:00Z");
            requestBody.put("dataEndTime", endDate + "T23:59:59Z");

            // Amazon搜索词报告使用周期性统计
            Map<String, String> reportOptions = new HashMap<>();
            reportOptions.put("reportPeriod", "WEEK");
            reportOptions.put("brandId", "2970732");
            requestBody.put("reportOptions", reportOptions);

            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<SpReportBaseResponse> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.POST, entity, SpReportBaseResponse.class
            );

            if ((response.getStatusCode() != HttpStatus.ACCEPTED &&
                    response.getStatusCode() != HttpStatus.OK) ||
                    response.getBody() == null) {
                log.error("创建Amazon搜索词报告失败 - 状态码: {}", response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建Amazon搜索词报告失败");
            }

            log.info("Amazon搜索词报告创建成功 - 报告ID: {}", response.getBody().getReportId());
            return response.getBody();

        } catch (Exception e) {
            log.error("创建Amazon搜索词报告异常: {}", e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建Amazon搜索词报告异常: " + e.getMessage());
        }
    }

    /**
     * 创建搜索词前三商品报告
     * 对应Amazon SP-API的Brand Analytics Search Terms Report
     * 专门用于获取每个搜索词对应的前三名商品信息（点击最多的ASIN）
     *
     * @param accessToken SP-API访问令牌
     * @param marketplaceIds 市场ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 报告创建响应
     */
    public SpReportBaseResponse createSearchTermsTopProductsReport(String accessToken,
                                                                   List<String> marketplaceIds,
                                                                   String startDate,
                                                                   String endDate) {
        try {
            log.info("创建搜索词前三商品报告 - 市场: {}, 日期范围: {} 到 {}", marketplaceIds, startDate, endDate);

            String url = reports_url + "/reports/2021-06-30/reports";

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("reportType", "GET_BRAND_ANALYTICS_SEARCH_TERMS_REPORT");
            requestBody.put("marketplaceIds", marketplaceIds);
            requestBody.put("dataStartTime", startDate + "T00:00:00Z");
            requestBody.put("dataEndTime", endDate + "T23:59:59Z");

            // 专门配置用于获取前三商品信息的报告选项
            Map<String, String> reportOptions = new HashMap<>();
            reportOptions.put("reportPeriod", "DAY");
            reportOptions.put("dateGranularity", "DAY");
            reportOptions.put("includeTopClickedAsins", "true");  // 包含点击最多的ASIN
            reportOptions.put("maxResults", "3");  // 限制为前3个商品
            reportOptions.put("brandId", "2970732");
            requestBody.put("reportOptions", reportOptions);

            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<SpReportBaseResponse> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.POST, entity, SpReportBaseResponse.class
            );

            if ((response.getStatusCode() != HttpStatus.ACCEPTED &&
                    response.getStatusCode() != HttpStatus.OK) ||
                    response.getBody() == null) {
                log.error("创建搜索词前三商品报告失败 - 状态码: {}", response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建搜索词前三商品报告失败");
            }

            log.info("搜索词前三商品报告创建成功 - 报告ID: {}", response.getBody().getReportId());
            return response.getBody();

        } catch (Exception e) {
            log.error("创建搜索词前三商品报告异常: {}", e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建搜索词前三商品报告异常: " + e.getMessage());
        }
    }

    /**
     * 获取报告状态和下载链接
     *
     * @param accessToken SP-API访问令牌
     * @param reportId 报告ID
     * @return 报告状态响应
     */
    public SpReportStatusResponse getReportStatus(String accessToken, String reportId) {
        try {
            log.info("获取报告状态 - 报告ID: {}", reportId);
            
            String url = reports_url + "/reports/2021-06-30/reports/" + reportId;
            
            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<SpReportStatusResponse> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.GET, entity, SpReportStatusResponse.class
            );

            if (response.getStatusCode() != HttpStatus.OK || response.getBody() == null) {
                log.error("获取报告状态失败 - 报告ID: {}, 状态码: {}", reportId, response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "获取报告状态失败");
            }

            log.info("报告状态获取成功 - 报告ID: {}, 状态: {}", reportId, response.getBody().getProcessingStatus());
            return response.getBody();

        } catch (Exception e) {
            log.error("获取报告状态异常 - 报告ID: {}, 错误: {}", reportId, e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "获取报告状态异常: " + e.getMessage());
        }
    }

    /**
     * 下载报告数据
     * 根据Amazon SP API文档，不同类型的报告在下载时会返回不同的数据结构
     * 这里统一返回原始的JSON字符串，由调用方根据报告类型进行解析
     *
     * @param accessToken SP-API访问令牌
     * @param documentId 文档ID
     * @return 报告数据（JSON字符串）
     */
    public String downloadReportData(String accessToken, String documentId) {
        try {
            log.info("下载报告数据 - 文档ID: {}", documentId);
            
            String url = reports_url + "/reports/2021-06-30/documents/" + documentId;
            
            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.GET, entity, String.class
            );

            if (response.getStatusCode() != HttpStatus.OK || response.getBody() == null) {
                log.error("下载报告数据失败 - 文档ID: {}, 状态码: {}", documentId, response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "下载报告数据失败");
            }

            log.info("报告数据下载成功 - 文档ID: {}, 数据大小: {} bytes", documentId, response.getBody().length());
            return response.getBody();

        } catch (Exception e) {
            log.error("下载报告数据异常 - 文档ID: {}, 错误: {}", documentId, e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "下载报告数据异常: " + e.getMessage());
        }
    }

    /**
     * 创建HTTP请求头
     *
     * @param accessToken 访问令牌
     * @return HTTP请求头
     */
    private HttpHeaders createHeaders(String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("x-amz-access-token", accessToken); // 仅保留必需头
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept", "application/json");
        return headers;
    }



    /**
     * 获取昨天的日期字符串
     *
     * @return 昨天的日期 (YYYY-MM-DD格式)
     */
    public String getYesterdayDate() {
        return LocalDate.now().minusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);
    }

    /**
     * 获取一周前的日期字符串
     *
     * @return 一周前的日期 (YYYY-MM-DD格式)
     */
    public String getWeekAgoDate() {
        return LocalDate.now().minusDays(7).format(DateTimeFormatter.ISO_LOCAL_DATE);
    }
}
