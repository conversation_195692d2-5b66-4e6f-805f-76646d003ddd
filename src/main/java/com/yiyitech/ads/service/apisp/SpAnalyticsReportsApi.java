package com.yiyitech.ads.service.apisp;

import com.yiyitech.ads.model.response.SpReportBaseResponse;
import com.yiyitech.ads.model.response.SpReportStatusResponse;
import com.yiyitech.ads.model.response.SpRestrictedDataTokenResponse;

import com.yiyitech.ads.exception.BasicExceptionCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import com.yiyitech.support.exception.BusinessException;

import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * Amazon SP-API 分析报告服务
 * 用于获取Amazon SP-API的各种分析报告数据
 * 包括搜索词报告、搜索目录性能报告、市场篮分析等
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * &#064;ClassName  SpAnalyticsReportsApi.java
 * &#064;Description  Amazon SP-API分析报告服务，获取搜索词和类目相关数据
 * &#064;createTime  2025年01月31日
 */
@Slf4j
@Component
public class SpAnalyticsReportsApi {

    private final RestTemplate awsSignedRestTemplate;
    private final RdtManager rdtManager;

    public SpAnalyticsReportsApi(
            @Qualifier("awsSignedRestTemplate") RestTemplate awsSignedRestTemplate) {
        this.awsSignedRestTemplate = awsSignedRestTemplate;
        this.rdtManager = new RdtManager();
    }

    @Value("${url.reports_url}")
    private String reports_url;

    /**
     * RDT管理器 - 统一管理受限数据令牌的创建和使用
     * 减少代码重复和嵌套深度
     */
    private class RdtManager {
        private final Map<String, SpRestrictedDataTokenResponse> tokenCache = new ConcurrentHashMap<>();

        /**
         * 获取或创建RDT令牌
         */
        public String getOrCreateToken(String accessToken, String reportType, List<String> marketplaceIds) {
            String cacheKey = generateCacheKey(accessToken, reportType, marketplaceIds);

            SpRestrictedDataTokenResponse cachedToken = tokenCache.get(cacheKey);
            if (cachedToken != null && !isTokenExpired(cachedToken)) {
                log.debug("使用缓存的RDT令牌");
                return cachedToken.getRestrictedDataToken();
            }

            // 创建新的RDT令牌
            log.info("创建新的RDT令牌 - 报告类型: {}", reportType);
            List<Map<String, Object>> restrictedResources = createRestrictedResources(reportType, marketplaceIds);
            SpRestrictedDataTokenResponse newToken = createRestrictedDataToken(accessToken, restrictedResources);

            tokenCache.put(cacheKey, newToken);
            return newToken.getRestrictedDataToken();
        }

        /**
         * 使用RDT执行报告创建操作
         */
        public SpReportBaseResponse executeWithRdt(String accessToken, String reportType,
                                                  List<String> marketplaceIds, String startDate, String endDate,
                                                  Supplier<Map<String, String>> optionsSupplier) {
            try {
                String rdtToken = getOrCreateToken(accessToken, reportType, marketplaceIds);
                Map<String, String> reportOptions = optionsSupplier != null ? optionsSupplier.get() : getDefaultOptions();

                return createReportWithRDTAndOptions(rdtToken, marketplaceIds, startDate, endDate, reportType, reportOptions);
            } catch (Exception e) {
                log.error("使用RDT创建报告失败 - 报告类型: {}, 错误: {}", reportType, e.getMessage(), e);
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建报告失败: " + e.getMessage());
            }
        }

        private String generateCacheKey(String accessToken, String reportType, List<String> marketplaceIds) {
            return String.format("%s_%s_%s",
                accessToken.substring(0, Math.min(10, accessToken.length())),
                reportType,
                String.join(",", marketplaceIds));
        }

        private boolean isTokenExpired(SpRestrictedDataTokenResponse token) {
            // 简单的过期检查，实际应该基于创建时间和过期时间
            return false; // 暂时不实现复杂的过期逻辑
        }

        private Map<String, String> getDefaultOptions() {
            Map<String, String> options = new HashMap<>();
            options.put("reportPeriod", "WEEK");
            options.put("brandId", "2970732");
            return options;
        }

        private List<Map<String, Object>> createRestrictedResources(String reportType, List<String> marketplaceIds) {
            List<Map<String, Object>> restrictedResources = new ArrayList<>();
            Map<String, Object> resource = new HashMap<>();
            resource.put("method", "POST");
            resource.put("path", "/reports/2021-06-30/reports");
            restrictedResources.add(resource);
            return restrictedResources;
        }
    }

    /**
     * 创建受限数据令牌(Restricted Data Token)
     * 根据Amazon SP-API文档，某些报告需要使用RDT来访问受限数据
     *
     * @param accessToken SP-API访问令牌
     * @param restrictedResources 受限资源列表
     * @return 受限数据令牌响应
     */
    public SpRestrictedDataTokenResponse createRestrictedDataToken(String accessToken,
                                                                  List<Map<String, Object>> restrictedResources) {
        try {
            log.info("创建受限数据令牌 - 受限资源数量: {}", restrictedResources.size());

            // 使用现有的reports_url配置，tokens API和reports API使用相同的基础URL
            String url = reports_url + "/tokens/2021-03-01/restrictedDataToken";

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("restrictedResources", restrictedResources);

            // 记录请求详情（用于调试）
            log.debug("RDT请求URL: {}", url);
            log.debug("RDT请求体: {}", requestBody);

            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<SpRestrictedDataTokenResponse> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.POST, entity, SpRestrictedDataTokenResponse.class
            );

            if (response.getStatusCode() != HttpStatus.OK || response.getBody() == null) {
                log.error("创建受限数据令牌失败 - 状态码: {}, 响应体: {}", response.getStatusCode(), response.getBody());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建受限数据令牌失败");
            }

            SpRestrictedDataTokenResponse rdtResponse = response.getBody();
            if (!rdtResponse.isSuccess()) {
                log.error("受限数据令牌响应包含错误: {}", rdtResponse.getFullErrorMessage());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "RDT创建失败: " + rdtResponse.getFullErrorMessage());
            }

            log.info("受限数据令牌创建成功 - 令牌过期时间: {} 秒", rdtResponse.getExpiresIn());
            return rdtResponse;

        } catch (Exception e) {
            log.error("创建受限数据令牌异常: {}", e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建受限数据令牌异常: " + e.getMessage());
        }
    }





    /**
     * 创建Amazon搜索词报告
     * 对应Amazon SP-API的Amazon Search Terms Report
     * 用于获取客户搜索词数据，显示客户在Amazon上搜索的词汇以及这些搜索如何转化为产品销售
     *
     * @param accessToken SP-API访问令牌
     * @param marketplaceIds 市场ID列表
     * @param startDate 开始日期 (YYYY-MM-DD)
     * @param endDate 结束日期 (YYYY-MM-DD)
     * @return 报告创建响应
     */
    public SpReportBaseResponse createSearchQueryPerformanceReport(String accessToken,
                                                                   List<String> marketplaceIds,
                                                                   String startDate,
                                                                   String endDate) {
        log.info("创建Amazon搜索词报告 - 开始日期: {}, 结束日期: {}, 市场: {}", startDate, endDate, marketplaceIds);

        String reportType = "GET_BRAND_ANALYTICS_SEARCH_TERMS_REPORT";
        return rdtManager.executeWithRdt(accessToken, reportType, marketplaceIds, startDate, endDate, null);
    }





    /**
     * 使用RDT和自定义选项创建报告
     */
    private SpReportBaseResponse createReportWithRDTAndOptions(String restrictedDataToken, List<String> marketplaceIds,
                                                              String startDate, String endDate, String reportType,
                                                              Map<String, String> customReportOptions) {
        String url = reports_url + "/reports/2021-06-30/reports";

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("reportType", reportType);
        requestBody.put("marketplaceIds", marketplaceIds);
        requestBody.put("dataStartTime", startDate + "T00:00:00Z");
        requestBody.put("dataEndTime", endDate + "T23:59:59Z");

        // 合并默认选项和自定义选项
        Map<String, String> reportOptions = new HashMap<>();
        reportOptions.put("reportPeriod", "WEEK");
        reportOptions.put("brandId","2970732");

        // 添加自定义选项（会覆盖默认选项）
        if (customReportOptions != null) {
            reportOptions.putAll(customReportOptions);
        }

        requestBody.put("reportOptions", reportOptions);

        HttpHeaders headers = createHeadersWithRDT(restrictedDataToken);
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

        log.debug("创建报告请求 - URL: {}, 请求体: {}", url, requestBody);

        ResponseEntity<SpReportBaseResponse> response = awsSignedRestTemplate.exchange(
                url, HttpMethod.POST, entity, SpReportBaseResponse.class
        );

        if ((response.getStatusCode() != HttpStatus.ACCEPTED &&
                response.getStatusCode() != HttpStatus.OK) ||
                response.getBody() == null) {
            log.error("创建报告失败 - 状态码: {} 响应体: {}", response.getStatusCode(), response.getBody());
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建报告失败");
        }

        log.info("报告创建成功（使用RDT） - 报告类型: {}, 报告ID: {}", reportType, response.getBody().getReportId());
        return response.getBody();
    }
    /**
     * 创建搜索目录性能报告
     * 对应Amazon SP-API的Search Catalog Performance Report
     * 用于获取搜索词与商品类目的关联性能数据
     *
     * @param accessToken SP-API访问令牌
     * @param marketplaceIds 市场ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 报告创建响应
     */
    public SpReportBaseResponse createSearchCatalogPerformanceReport(String accessToken,
                                                                     List<String> marketplaceIds,
                                                                     String startDate,
                                                                     String endDate) {
        log.info("创建搜索目录性能报告 - 开始日期: {}, 结束日期: {}, 市场: {}", startDate, endDate, marketplaceIds);

        String reportType = "GET_BRAND_ANALYTICS_SEARCH_CATALOG_PERFORMANCE_REPORT";
        return rdtManager.executeWithRdt(accessToken, reportType, marketplaceIds, startDate, endDate, null);
    }

    /**
     * 创建Amazon搜索词报告
     * 对应Amazon SP-API的Amazon Search Terms Report
     * 用于获取Amazon平台的搜索词数据和趋势
     *
     * @param accessToken SP-API访问令牌
     * @param marketplaceIds 市场ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 报告创建响应
     */
    public SpReportBaseResponse createAmazonSearchTermsReport(String accessToken,
                                                              List<String> marketplaceIds,
                                                              String startDate,
                                                              String endDate) {
        log.info("创建Amazon搜索词报告 - 开始日期: {}, 结束日期: {}, 市场: {}", startDate, endDate, marketplaceIds);

        String reportType = "GET_BRAND_ANALYTICS_AMAZON_SEARCH_TERMS_REPORT";

        // Amazon搜索词报告使用周期性统计
        Supplier<Map<String, String>> optionsSupplier = () -> {
            Map<String, String> options = new HashMap<>();
            options.put("reportPeriod", "WEEK");
            options.put("brandId", "2970732");
            return options;
        };

        return rdtManager.executeWithRdt(accessToken, reportType, marketplaceIds, startDate, endDate, optionsSupplier);
    }

    /**
     * 创建搜索词前三商品报告
     * 对应Amazon SP-API的Brand Analytics Search Terms Report
     * 专门用于获取每个搜索词对应的前三名商品信息（点击最多的ASIN）
     *
     * @param accessToken SP-API访问令牌
     * @param marketplaceIds 市场ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 报告创建响应
     */
    public SpReportBaseResponse createSearchTermsTopProductsReport(String accessToken,
                                                                   List<String> marketplaceIds,
                                                                   String startDate,
                                                                   String endDate) {
        log.info("创建搜索词前三商品报告 - 市场: {}, 日期范围: {} 到 {}", marketplaceIds, startDate, endDate);

        String reportType = "GET_BRAND_ANALYTICS_SEARCH_TERMS_REPORT";

        // 专门配置用于获取前三商品信息的报告选项
        Supplier<Map<String, String>> optionsSupplier = () -> {
            Map<String, String> options = new HashMap<>();
            options.put("reportPeriod", "DAY");
            options.put("dateGranularity", "DAY");
            options.put("includeTopClickedAsins", "true");  // 包含点击最多的ASIN
            options.put("maxResults", "3");  // 限制为前3个商品
            options.put("brandId", "2970732");
            return options;
        };

        return rdtManager.executeWithRdt(accessToken, reportType, marketplaceIds, startDate, endDate, optionsSupplier);
    }

    /**
     * 获取报告状态和下载链接
     *
     * @param accessToken SP-API访问令牌
     * @param reportId 报告ID
     * @return 报告状态响应
     */
    public SpReportStatusResponse getReportStatus(String accessToken, String reportId) {
        try {
            log.info("获取报告状态 - 报告ID: {}", reportId);
            
            String url = reports_url + "/reports/2021-06-30/reports/" + reportId;
            
            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<SpReportStatusResponse> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.GET, entity, SpReportStatusResponse.class
            );

            if (response.getStatusCode() != HttpStatus.OK || response.getBody() == null) {
                log.error("获取报告状态失败 - 报告ID: {}, 状态码: {}", reportId, response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "获取报告状态失败");
            }

            log.info("报告状态获取成功 - 报告ID: {}, 状态: {}", reportId, response.getBody().getProcessingStatus());
            return response.getBody();

        } catch (Exception e) {
            log.error("获取报告状态异常 - 报告ID: {}, 错误: {}", reportId, e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "获取报告状态异常: " + e.getMessage());
        }
    }

    /**
     * 下载报告数据
     * 根据Amazon SP API文档，不同类型的报告在下载时会返回不同的数据结构
     * 这里统一返回原始的JSON字符串，由调用方根据报告类型进行解析
     *
     * @param accessToken SP-API访问令牌
     * @param documentId 文档ID
     * @return 报告数据（JSON字符串）
     */
    public String downloadReportData(String accessToken, String documentId) {
        try {
            log.info("下载报告数据 - 文档ID: {}", documentId);
            
            String url = reports_url + "/reports/2021-06-30/documents/" + documentId;
            
            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.GET, entity, String.class
            );

            if (response.getStatusCode() != HttpStatus.OK || response.getBody() == null) {
                log.error("下载报告数据失败 - 文档ID: {}, 状态码: {}", documentId, response.getStatusCode());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "下载报告数据失败");
            }

            log.info("报告数据下载成功 - 文档ID: {}, 数据大小: {} bytes", documentId, response.getBody().length());
            return response.getBody();

        } catch (Exception e) {
            log.error("下载报告数据异常 - 文档ID: {}, 错误: {}", documentId, e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "下载报告数据异常: " + e.getMessage());
        }
    }

    /**
     * 创建HTTP请求头
     *
     * @param accessToken 访问令牌
     * @return HTTP请求头
     */
    private HttpHeaders createHeaders(String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("x-amz-access-token", accessToken); // 仅保留必需头
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept", "application/json");
        return headers;
    }

    /**
     * 创建带受限数据令牌的HTTP请求头
     *
     * @param restrictedDataToken 受限数据令牌
     * @return HTTP请求头
     */
    private HttpHeaders createHeadersWithRDT(String restrictedDataToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("x-amz-access-token", restrictedDataToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept", "application/json");
        return headers;
    }

    /**
     * 获取昨天的日期字符串
     *
     * @return 昨天的日期 (YYYY-MM-DD格式)
     */
    public String getYesterdayDate() {
        return LocalDate.now().minusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE);
    }

    /**
     * 获取一周前的日期字符串
     *
     * @return 一周前的日期 (YYYY-MM-DD格式)
     */
    public String getWeekAgoDate() {
        return LocalDate.now().minusDays(7).format(DateTimeFormatter.ISO_LOCAL_DATE);
    }
}
