package com.yiyitech.ads.service;

import com.yiyitech.ads.model.AdsSearchTermAnalyticsModel;
import com.yiyitech.ads.model.AdsSearchTermCategoryRelationModel;
import com.yiyitech.ads.model.AdsSearchTermProductRankingModel;

import java.util.List;
import java.util.Map;

/**
 * 搜索词分析服务接口
 * 用于处理搜索词相关的分析数据
 * 包括搜索词数据获取、类目关联、商品排名等功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @ClassName AdsSearchTermAnalyticsService.java
 * @Description 搜索词分析服务接口
 * @createTime 2025年01月31日
 */
public interface AdsSearchTermAnalyticsService {

    /**
     * 同步搜索词分析数据
     * 从Amazon SP-API获取搜索词分析数据并存储到数据库
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param marketplaceId 市场ID
     * @param startDate 开始日期 (YYYY-MM-DD)
     * @param endDate 结束日期 (YYYY-MM-DD)
     * @return 同步结果统计
     */
    Map<String, Object> syncSearchTermAnalyticsData(String accountId, Long profileId, 
                                                   String marketplaceId, String startDate, String endDate);

    /**
     * 同步搜索词与类目关联数据
     * 分析搜索词与商品类目的关联关系，按点击量排序
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param marketplaceId 市场ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 同步结果统计
     */
    Map<String, Object> syncSearchTermCategoryRelations(String accountId, Long profileId,
                                                       String marketplaceId, String startDate, String endDate);

    /**
     * 同步搜索词商品排名数据
     * 获取每个搜索词关联的点击量前三商品信息
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param marketplaceId 市场ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 同步结果统计
     */
    Map<String, Object> syncSearchTermProductRankings(String accountId, Long profileId,
                                                     String marketplaceId, String startDate, String endDate);

    /**
     * 获取搜索词分析数据列表（原版本，保留兼容性）
     *
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词（可选）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 搜索词分析数据列表
     */
    Map<String, Object> getSearchTermAnalyticsList(String accountId, Long profileId, String searchTerm,
                                                  String startDate, String endDate, Integer pageNum, Integer pageSize);

    /**
     * 获取搜索词分析数据列表（简化版）
     * 只需要时间和类目两个可选条件
     *
     * @param reportDate 报告日期（可选，格式：YYYY-MM-DD）
     * @param category 类目名称（可选）
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 搜索词分析数据列表
     */
    Map<String, Object> getSearchTermAnalyticsListSimple(String reportDate, String category,
                                                         Integer pageNum, Integer pageSize);

    /**
     * 获取搜索词关联的类目数据
     * 返回指定搜索词关联的前三个类目信息
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @return 关联类目列表
     */
    List<AdsSearchTermCategoryRelationModel> getSearchTermCategories(String accountId, Long profileId, String searchTerm);

    /**
     * 获取搜索词关联的商品排名数据
     * 返回指定搜索词关联的前三个商品信息
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @return 商品排名列表
     */
    List<AdsSearchTermProductRankingModel> getSearchTermProductRankings(String accountId, Long profileId, String searchTerm);

    /**
     * 获取搜索词详细信息
     * 包含搜索词基本信息、关联类目、关联商品等完整数据
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @return 搜索词详细信息
     */
    Map<String, Object> getSearchTermDetails(String accountId, Long profileId, String searchTerm);

    /**
     * 批量更新搜索词数据
     *
     * @param searchTermAnalyticsList 搜索词分析数据列表
     * @return 更新结果
     */
    boolean batchUpdateSearchTermAnalytics(List<AdsSearchTermAnalyticsModel> searchTermAnalyticsList);

    /**
     * 获取第一个可用的Amazon账户ID
     * 从硬编码的账户列表中获取第一个有效的账户
     *
     * @return 账户ID
     */
    String getFirstAvailableAccountId();

    /**
     * 批量更新搜索词类目关联数据
     * 
     * @param categoryRelationList 类目关联数据列表
     * @return 更新结果
     */
    boolean batchUpdateSearchTermCategoryRelations(List<AdsSearchTermCategoryRelationModel> categoryRelationList);

    /**
     * 批量更新搜索词商品排名数据
     * 
     * @param productRankingList 商品排名数据列表
     * @return 更新结果
     */
    boolean batchUpdateSearchTermProductRankings(List<AdsSearchTermProductRankingModel> productRankingList);

    /**
     * 获取热门搜索词列表
     * 按搜索量排序返回热门搜索词
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param limit 返回数量限制
     * @return 热门搜索词列表
     */
    List<AdsSearchTermAnalyticsModel> getTopSearchTerms(String accountId, Long profileId, Integer limit);

    /**
     * 获取搜索词趋势数据
     * 返回搜索词在指定时间段内的趋势变化
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 趋势数据
     */
    Map<String, Object> getSearchTermTrends(String accountId, Long profileId, String searchTerm,
                                           String startDate, String endDate);

    /**
     * 分析搜索词竞争情况
     * 分析指定搜索词的竞争强度和机会
     * 
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param searchTerm 搜索词
     * @return 竞争分析结果
     */
    Map<String, Object> analyzeSearchTermCompetition(String accountId, Long profileId, String searchTerm);

    /**
     * 获取搜索词建议
     * 基于现有数据为用户推荐相关搜索词
     *
     * @param accountId 账户ID
     * @param profileId 配置文件ID
     * @param seedKeyword 种子关键词
     * @param limit 返回数量限制
     * @return 搜索词建议列表
     */
    List<String> getSearchTermSuggestions(String accountId, Long profileId, String seedKeyword, Integer limit);

    /**
     * 搜索词与类目组合查询（单个搜索词）
     * 根据搜索词查询相关信息，同时返回该搜索词点击量前三的类目
     *
     * @param searchTerm 搜索词（必填）
     * @param reportDate 报告日期（可选，格式：YYYY-MM-DD）
     * @return 搜索词信息和前三类目数据
     */
    Map<String, Object> getSearchTermWithTopCategories(String searchTerm, String reportDate);

    /**
     * 搜索词与类目组合查询（所有数据）
     * 查询所有搜索词数据，每个搜索词包含其点击量前三的类目
     *
     * @param reportDate 报告日期（可选，格式：YYYY-MM-DD）
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 所有搜索词信息和对应的前三类目数据
     */
    Map<String, Object> getAllSearchTermsWithTopCategories(String reportDate, Integer pageNum, Integer pageSize);

    /**
     * 获取类目点击量最高的关键词及其前三商品（带匹配度排序）
     * 先筛选出类目点击量最高的关键词，然后获取每个关键词的前三商品，
     * 根据商品标题与关键词的匹配度进行排序
     *
     * @param reportDate 报告日期（可选，格式：YYYY-MM-DD）
     * @param topKeywordsLimit 获取前N个关键词（默认10，最大50）
     * @return 关键词及其匹配度排序后的前三商品
     */
    Map<String, Object> getTopKeywordsWithMatchedProducts(String reportDate, Integer topKeywordsLimit);
}
