# RDT服务使用指南

## 🎯 概述

我已经为您创建了一个独立的RDT（Restricted Data Token）服务 `SpRestrictedDataTokenService`，您可以在需要的时候单独调用，而不需要嵌入到其他业务逻辑中。

## 📁 文件位置

```
src/main/java/com/yiyitech/ads/service/apisp/SpRestrictedDataTokenService.java
```

## 🔧 主要功能

### **1. 智能缓存管理**
- 自动缓存RDT令牌，避免频繁创建
- 自动过期检测，提前5分钟刷新令牌
- 支持手动清除缓存

### **2. 多种创建方式**
- 基于报告类型自动创建
- 支持自定义受限资源
- 带缓存和不带缓存的选项

### **3. 便捷的工具方法**
- 创建带RDT的HTTP请求头
- 缓存统计和管理
- 完整的错误处理和日志

## 🚀 使用方式

### **方式1：注入服务使用（推荐）**

```java
@Service
public class YourReportService {
    
    @Autowired
    private SpRestrictedDataTokenService rdtService;
    
    @Autowired
    private RestTemplate awsSignedRestTemplate;
    
    public SpReportBaseResponse createReportWithRDT(String accessToken, 
                                                   List<String> marketplaceIds,
                                                   String startDate, 
                                                   String endDate) {
        // 1. 获取RDT令牌（自动缓存）
        String rdtToken = rdtService.getOrCreateToken(
            accessToken, 
            "GET_BRAND_ANALYTICS_SEARCH_TERMS_REPORT", 
            marketplaceIds
        );
        
        // 2. 创建带RDT的请求头
        HttpHeaders headers = rdtService.createHeadersWithRDT(rdtToken);
        
        // 3. 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("reportType", "GET_BRAND_ANALYTICS_SEARCH_TERMS_REPORT");
        requestBody.put("marketplaceIds", marketplaceIds);
        requestBody.put("dataStartTime", startDate + "T00:00:00Z");
        requestBody.put("dataEndTime", endDate + "T23:59:59Z");
        
        Map<String, String> reportOptions = new HashMap<>();
        reportOptions.put("reportPeriod", "WEEK");
        reportOptions.put("brandId", "2970732");
        requestBody.put("reportOptions", reportOptions);
        
        // 4. 发送请求
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
        String url = reports_url + "/reports/2021-06-30/reports";
        
        ResponseEntity<SpReportBaseResponse> response = awsSignedRestTemplate.exchange(
            url, HttpMethod.POST, entity, SpReportBaseResponse.class
        );
        
        return response.getBody();
    }
}
```

### **方式2：在现有的SpAnalyticsReportsApi中使用**

```java
@Service
public class SpAnalyticsReportsApi {
    
    @Autowired
    private SpRestrictedDataTokenService rdtService;
    
    // 添加一个使用RDT的报告创建方法
    public SpReportBaseResponse createSearchQueryPerformanceReportWithRDT(String accessToken,
                                                                          List<String> marketplaceIds,
                                                                          String startDate,
                                                                          String endDate) {
        try {
            log.info("使用RDT创建Amazon搜索词报告 - 开始日期: {}, 结束日期: {}, 市场: {}", startDate, endDate, marketplaceIds);

            // 获取RDT令牌
            String rdtToken = rdtService.getOrCreateToken(
                accessToken, 
                "GET_BRAND_ANALYTICS_SEARCH_TERMS_REPORT", 
                marketplaceIds
            );

            String url = reports_url + "/reports/2021-06-30/reports";

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("reportType", "GET_BRAND_ANALYTICS_SEARCH_TERMS_REPORT");
            requestBody.put("marketplaceIds", marketplaceIds);
            requestBody.put("dataStartTime", startDate + "T00:00:00Z");
            requestBody.put("dataEndTime", endDate + "T23:59:59Z");

            Map<String, String> reportOptions = new HashMap<>();
            reportOptions.put("reportPeriod", "WEEK");
            reportOptions.put("brandId", "2970732");
            requestBody.put("reportOptions", reportOptions);

            // 使用RDT请求头
            HttpHeaders headers = rdtService.createHeadersWithRDT(rdtToken);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<SpReportBaseResponse> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.POST, entity, SpReportBaseResponse.class
            );

            if ((response.getStatusCode() != HttpStatus.ACCEPTED &&
                    response.getStatusCode() != HttpStatus.OK) ||
                    response.getBody() == null) {
                log.error("创建报告失败 - 状态码: {} 响应体: {}", response.getStatusCode(), response.getBody());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建报告失败");
            }

            log.info("报告创建成功（使用RDT） - 报告类型: {}, 报告ID: {}", "GET_BRAND_ANALYTICS_SEARCH_TERMS_REPORT", response.getBody().getReportId());
            return response.getBody();

        } catch (Exception e) {
            log.error("创建搜索词性能报告异常: {}", e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建搜索词性能报告异常: " + e.getMessage());
        }
    }
}
```

### **方式3：自定义受限资源**

```java
// 创建自定义的受限资源配置
List<Map<String, Object>> customResources = new ArrayList<>();
Map<String, Object> resource = new HashMap<>();
resource.put("method", "POST");
resource.put("path", "/reports/2021-06-30/reports");
resource.put("dataElements", Arrays.asList("searchTerm", "impressions", "clicks"));

customResources.add(resource);

// 使用自定义资源创建RDT
SpRestrictedDataTokenResponse rdtResponse = rdtService.createTokenForCustomResources(
    accessToken, 
    customResources
);

String rdtToken = rdtResponse.getRestrictedDataToken();
```

## 🛠️ API方法说明

### **主要方法**

| 方法名 | 描述 | 使用场景 |
|--------|------|----------|
| `getOrCreateToken()` | 获取或创建RDT令牌（带缓存） | 常规使用，推荐 |
| `createTokenForReport()` | 为特定报告类型创建令牌 | 不需要缓存时 |
| `createTokenForCustomResources()` | 为自定义资源创建令牌 | 特殊需求 |
| `createHeadersWithRDT()` | 创建带RDT的HTTP请求头 | 构建请求时 |

### **缓存管理方法**

| 方法名 | 描述 |
|--------|------|
| `clearTokenCache()` | 清除指定令牌缓存 |
| `clearExpiredTokens()` | 清除所有过期令牌 |
| `getCacheStats()` | 获取缓存统计信息 |

## 📊 缓存机制

### **自动缓存**
- 基于 `accessToken + reportType + marketplaceIds` 生成缓存键
- 令牌提前5分钟过期，确保安全性
- 线程安全的ConcurrentHashMap实现

### **缓存统计**
```java
Map<String, Object> stats = rdtService.getCacheStats();
System.out.println("总缓存数量: " + stats.get("totalCached"));
System.out.println("有效令牌数: " + stats.get("validCount"));
System.out.println("过期令牌数: " + stats.get("expiredCount"));
```

## ⚠️ 注意事项

### **1. 依赖注入**
确保在Spring容器中正确注入：
```java
@Autowired
private SpRestrictedDataTokenService rdtService;
```

### **2. 错误处理**
服务内置完整的错误处理，会抛出BusinessException：
```java
try {
    String token = rdtService.getOrCreateToken(accessToken, reportType, marketplaceIds);
} catch (BusinessException e) {
    log.error("RDT创建失败: {}", e.getMessage());
    // 处理错误
}
```

### **3. 令牌安全**
- 令牌在日志中会被脱敏显示
- 自动过期管理，无需手动处理
- 支持手动清除敏感缓存

## 🎉 优势

✅ **按需使用**：只在需要时调用，不影响现有代码  
✅ **智能缓存**：自动管理令牌生命周期，提高性能  
✅ **灵活配置**：支持多种创建方式和自定义资源  
✅ **完整日志**：详细的操作日志，便于调试  
✅ **线程安全**：支持并发访问，适合生产环境  

现在您可以在任何需要RDT的地方简单地注入这个服务并调用相应的方法，完全独立于其他业务逻辑！🚀
