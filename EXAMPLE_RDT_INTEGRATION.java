// 示例：如何在现有的SpAnalyticsReportsApi中集成RDT服务

@Slf4j
@Component
public class SpAnalyticsReportsApi {

    private final RestTemplate awsSignedRestTemplate;
    
    // 可选：注入RDT服务，当需要时使用
    @Autowired(required = false)
    private SpRestrictedDataTokenService rdtService;

    @Value("${url.reports_url}")
    private String reports_url;

    public SpAnalyticsReportsApi(
            @Qualifier("awsSignedRestTemplate") RestTemplate awsSignedRestTemplate) {
        this.awsSignedRestTemplate = awsSignedRestTemplate;
    }

    // 原有的普通方法保持不变
    public SpReportBaseResponse createSearchQueryPerformanceReport(String accessToken,
                                                                   List<String> marketplaceIds,
                                                                   String startDate,
                                                                   String endDate) {
        // 原有的实现保持不变...
        return createReportDirectly(accessToken, marketplaceIds, startDate, endDate, 
                                   "GET_BRAND_ANALYTICS_SEARCH_TERMS_REPORT");
    }

    // 新增：使用RDT的版本（当需要时调用）
    public SpReportBaseResponse createSearchQueryPerformanceReportWithRDT(String accessToken,
                                                                          List<String> marketplaceIds,
                                                                          String startDate,
                                                                          String endDate) {
        if (rdtService == null) {
            log.warn("RDT服务未注入，回退到普通方法");
            return createSearchQueryPerformanceReport(accessToken, marketplaceIds, startDate, endDate);
        }

        try {
            log.info("使用RDT创建Amazon搜索词报告 - 开始日期: {}, 结束日期: {}, 市场: {}", startDate, endDate, marketplaceIds);

            // 1. 获取RDT令牌
            String rdtToken = rdtService.getOrCreateToken(
                accessToken, 
                "GET_BRAND_ANALYTICS_SEARCH_TERMS_REPORT", 
                marketplaceIds
            );

            // 2. 使用RDT创建报告
            return createReportWithRDTToken(rdtToken, marketplaceIds, startDate, endDate, 
                                          "GET_BRAND_ANALYTICS_SEARCH_TERMS_REPORT");

        } catch (Exception e) {
            log.error("使用RDT创建报告失败，回退到普通方法: {}", e.getMessage());
            return createSearchQueryPerformanceReport(accessToken, marketplaceIds, startDate, endDate);
        }
    }

    // 智能方法：自动选择是否使用RDT
    public SpReportBaseResponse createSearchQueryPerformanceReportSmart(String accessToken,
                                                                        List<String> marketplaceIds,
                                                                        String startDate,
                                                                        String endDate,
                                                                        boolean useRDT) {
        if (useRDT && rdtService != null) {
            return createSearchQueryPerformanceReportWithRDT(accessToken, marketplaceIds, startDate, endDate);
        } else {
            return createSearchQueryPerformanceReport(accessToken, marketplaceIds, startDate, endDate);
        }
    }

    // 私有方法：直接创建报告（不使用RDT）
    private SpReportBaseResponse createReportDirectly(String accessToken,
                                                     List<String> marketplaceIds,
                                                     String startDate,
                                                     String endDate,
                                                     String reportType) {
        try {
            String url = reports_url + "/reports/2021-06-30/reports";

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("reportType", reportType);
            requestBody.put("marketplaceIds", marketplaceIds);
            requestBody.put("dataStartTime", startDate + "T00:00:00Z");
            requestBody.put("dataEndTime", endDate + "T23:59:59Z");

            Map<String, String> reportOptions = new HashMap<>();
            reportOptions.put("reportPeriod", "WEEK");
            reportOptions.put("brandId", "2970732");
            requestBody.put("reportOptions", reportOptions);

            HttpHeaders headers = createHeaders(accessToken);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<SpReportBaseResponse> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.POST, entity, SpReportBaseResponse.class
            );

            if ((response.getStatusCode() != HttpStatus.ACCEPTED &&
                    response.getStatusCode() != HttpStatus.OK) ||
                    response.getBody() == null) {
                log.error("创建报告失败 - 状态码: {} 响应体: {}", response.getStatusCode(), response.getBody());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建报告失败");
            }

            log.info("报告创建成功 - 报告类型: {}, 报告ID: {}", reportType, response.getBody().getReportId());
            return response.getBody();

        } catch (Exception e) {
            log.error("创建报告异常: {}", e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建报告异常: " + e.getMessage());
        }
    }

    // 私有方法：使用RDT令牌创建报告
    private SpReportBaseResponse createReportWithRDTToken(String rdtToken,
                                                         List<String> marketplaceIds,
                                                         String startDate,
                                                         String endDate,
                                                         String reportType) {
        try {
            String url = reports_url + "/reports/2021-06-30/reports";

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("reportType", reportType);
            requestBody.put("marketplaceIds", marketplaceIds);
            requestBody.put("dataStartTime", startDate + "T00:00:00Z");
            requestBody.put("dataEndTime", endDate + "T23:59:59Z");

            Map<String, String> reportOptions = new HashMap<>();
            reportOptions.put("reportPeriod", "WEEK");
            reportOptions.put("brandId", "2970732");
            requestBody.put("reportOptions", reportOptions);

            // 使用RDT服务创建请求头
            HttpHeaders headers = rdtService.createHeadersWithRDT(rdtToken);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<SpReportBaseResponse> response = awsSignedRestTemplate.exchange(
                    url, HttpMethod.POST, entity, SpReportBaseResponse.class
            );

            if ((response.getStatusCode() != HttpStatus.ACCEPTED &&
                    response.getStatusCode() != HttpStatus.OK) ||
                    response.getBody() == null) {
                log.error("创建报告失败 - 状态码: {} 响应体: {}", response.getStatusCode(), response.getBody());
                throw new BusinessException(BasicExceptionCode.BASIC_CODE, "创建报告失败");
            }

            log.info("报告创建成功（使用RDT） - 报告类型: {}, 报告ID: {}", reportType, response.getBody().getReportId());
            return response.getBody();

        } catch (Exception e) {
            log.error("使用RDT创建报告异常: {}", e.getMessage(), e);
            throw new BusinessException(BasicExceptionCode.BASIC_CODE, "使用RDT创建报告异常: " + e.getMessage());
        }
    }

    // 原有的工具方法保持不变
    private HttpHeaders createHeaders(String accessToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("x-amz-access-token", accessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Accept", "application/json");
        return headers;
    }

    // 其他原有方法保持不变...
}

// ==================== 使用示例 ====================

@Service
public class ExampleUsageService {
    
    @Autowired
    private SpAnalyticsReportsApi spAnalyticsReportsApi;

    public void demonstrateUsage() {
        String accessToken = "your-access-token";
        List<String> marketplaceIds = Arrays.asList("ATVPDKIKX0DER");
        String startDate = "2025-08-01";
        String endDate = "2025-08-07";

        // 方式1：使用原有的普通方法
        SpReportBaseResponse response1 = spAnalyticsReportsApi.createSearchQueryPerformanceReport(
            accessToken, marketplaceIds, startDate, endDate
        );

        // 方式2：明确使用RDT方法
        SpReportBaseResponse response2 = spAnalyticsReportsApi.createSearchQueryPerformanceReportWithRDT(
            accessToken, marketplaceIds, startDate, endDate
        );

        // 方式3：智能选择（根据参数决定是否使用RDT）
        boolean needRDT = true; // 根据业务逻辑决定
        SpReportBaseResponse response3 = spAnalyticsReportsApi.createSearchQueryPerformanceReportSmart(
            accessToken, marketplaceIds, startDate, endDate, needRDT
        );
    }
}

// ==================== 配置示例 ====================

@Configuration
public class RDTConfiguration {
    
    // 可以通过配置决定是否启用RDT服务
    @Bean
    @ConditionalOnProperty(name = "amazon.sp-api.rdt.enabled", havingValue = "true")
    public SpRestrictedDataTokenService rdtService(@Qualifier("awsSignedRestTemplate") RestTemplate restTemplate) {
        return new SpRestrictedDataTokenService(restTemplate);
    }
}
